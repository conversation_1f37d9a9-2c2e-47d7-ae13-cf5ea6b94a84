import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class RefreshListView<T> extends StatefulWidget {
  const RefreshListView({
    super.key,
    required this.dataList,
    required this.itemBuilder,
    required this.onRefresh,
    required this.onLoadMore,
    this.isLoading = false,
    this.error,
    this.hasMore = true,
    this.separatorBuilder,
    this.emptyBuilder,
    this.errorBuilder,
    this.scrollDirection = Axis.vertical,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.itemCount,
    this.gridDelegate,
  });

  /// 数据列表
  final List<T> dataList;

  /// 构建列表项的回调函数
  final Widget Function(BuildContext context, T item, int index) itemBuilder;

  /// 下拉刷新回调
  final Future<void> Function() onRefresh;

  /// 上拉加载更多回调
  final Future<void> Function() onLoadMore;

  /// 是否正在加载
  final bool isLoading;

  /// 错误信息
  final dynamic error;

  /// 是否还有更多数据
  final bool hasMore;

  /// 构建分隔符的回调函数，仅在ListView模式下有效
  final Widget Function(BuildContext context, int index)? separatorBuilder;

  /// 构建空状态的回调函数
  final Widget Function(BuildContext context)? emptyBuilder;

  /// 构建错误状态的回调函数
  final Widget Function(BuildContext context, dynamic error)? errorBuilder;

  /// 滚动方向
  final Axis scrollDirection;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 滚动物理效果
  final ScrollPhysics? physics;

  /// 是否收缩包装
  final bool shrinkWrap;

  /// 手动指定项目数量（不设置则自动使用数据列表长度）
  final int? itemCount;

  /// 网格布局代理，如果设置则使用GridView，否则使用ListView
  final SliverGridDelegate? gridDelegate;

  @override
  State<RefreshListView<T>> createState() => _RefreshListViewState<T>();
}

class _RefreshListViewState<T> extends State<RefreshListView<T>> {
  final _refreshController = RefreshController();

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    try {
      await widget.onRefresh();
      _refreshController.refreshCompleted();
    } catch (error) {
      _refreshController.refreshFailed();
    }
  }

  Future<void> _onLoadMore() async {
    try {
      await widget.onLoadMore();
      if (widget.hasMore) {
        _refreshController.loadComplete();
      } else {
        _refreshController.loadNoData();
      }
    } catch (error) {
      _refreshController.loadFailed();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading && widget.dataList.isEmpty) {
      return SmartRefresher(
        enablePullDown: true,
        enablePullUp: false,
        onRefresh: _onRefresh,
        controller: _refreshController,
        child: const Center(
          child: CircularProgressIndicator.adaptive(),
        ),
      );
    }

    if (widget.error != null && widget.dataList.isEmpty) {
      return SmartRefresher(
        enablePullDown: true,
        enablePullUp: false,
        onRefresh: _onRefresh,
        controller: _refreshController,
        child: widget.errorBuilder != null
            ? widget.errorBuilder!(context, widget.error)
            : Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text('${widget.error}'),
                    TextButton(
                      onPressed: _onRefresh,
                      child: const Text('Retry'),
                    ),
                  ],
                ),
              ),
      );
    }

    if (widget.dataList.isEmpty) {
      return SmartRefresher(
        enablePullDown: true,
        enablePullUp: false,
        onRefresh: _onRefresh,
        controller: _refreshController,
        child: widget.emptyBuilder != null
            ? widget.emptyBuilder!(context)
            : const Center(
                child: Text('No data'),
              ),
      );
    }

    return SmartRefresher(
      enablePullDown: true,
      enablePullUp: widget.hasMore,
      onRefresh: _onRefresh,
      onLoading: widget.hasMore ? _onLoadMore : null,
      controller: _refreshController,
      child: _buildList(),
    );
  }

  Widget _buildList() {
    final itemCount = widget.itemCount ?? widget.dataList.length;

    if (widget.gridDelegate != null) {
      return GridView.builder(
        gridDelegate: widget.gridDelegate!,
        itemBuilder: (context, index) =>
            widget.itemBuilder(context, widget.dataList[index], index),
        itemCount: itemCount,
        padding: widget.padding,
        physics: widget.physics,
        shrinkWrap: widget.shrinkWrap,
        scrollDirection: widget.scrollDirection,
      );
    }

    if (widget.separatorBuilder != null) {
      return ListView.separated(
        itemBuilder: (context, index) =>
            widget.itemBuilder(context, widget.dataList[index], index),
        separatorBuilder: widget.separatorBuilder!,
        itemCount: itemCount,
        padding: widget.padding,
        physics: widget.physics,
        shrinkWrap: widget.shrinkWrap,
        scrollDirection: widget.scrollDirection,
      );
    }

    return ListView.builder(
      itemBuilder: (context, index) =>
          widget.itemBuilder(context, widget.dataList[index], index),
      itemCount: itemCount,
      padding: widget.padding,
      physics: widget.physics,
      shrinkWrap: widget.shrinkWrap,
      scrollDirection: widget.scrollDirection,
    );
  }
}
