// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'send_gift_result.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SendGiftResult _$SendGiftResultFromJson(Map<String, dynamic> json) {
  return _SendGiftResult.fromJson(json);
}

/// @nodoc
mixin _$SendGiftResult {
  String? get userId => throw _privateConstructorUsedError;
  int? get giftBagId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SendGiftResultCopyWith<SendGiftResult> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SendGiftResultCopyWith<$Res> {
  factory $SendGiftResultCopyWith(
          SendGiftResult value, $Res Function(SendGiftResult) then) =
      _$SendGiftResultCopyWithImpl<$Res, SendGiftResult>;
  @useResult
  $Res call({String? userId, int? giftBagId});
}

/// @nodoc
class _$SendGiftResultCopyWithImpl<$Res, $Val extends SendGiftResult>
    implements $SendGiftResultCopyWith<$Res> {
  _$SendGiftResultCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? giftBagId = freezed,
  }) {
    return _then(_value.copyWith(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      giftBagId: freezed == giftBagId
          ? _value.giftBagId
          : giftBagId // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SendGiftResultImplCopyWith<$Res>
    implements $SendGiftResultCopyWith<$Res> {
  factory _$$SendGiftResultImplCopyWith(_$SendGiftResultImpl value,
          $Res Function(_$SendGiftResultImpl) then) =
      __$$SendGiftResultImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String? userId, int? giftBagId});
}

/// @nodoc
class __$$SendGiftResultImplCopyWithImpl<$Res>
    extends _$SendGiftResultCopyWithImpl<$Res, _$SendGiftResultImpl>
    implements _$$SendGiftResultImplCopyWith<$Res> {
  __$$SendGiftResultImplCopyWithImpl(
      _$SendGiftResultImpl _value, $Res Function(_$SendGiftResultImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userId = freezed,
    Object? giftBagId = freezed,
  }) {
    return _then(_$SendGiftResultImpl(
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      giftBagId: freezed == giftBagId
          ? _value.giftBagId
          : giftBagId // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SendGiftResultImpl implements _SendGiftResult {
  const _$SendGiftResultImpl({this.userId, this.giftBagId});

  factory _$SendGiftResultImpl.fromJson(Map<String, dynamic> json) =>
      _$$SendGiftResultImplFromJson(json);

  @override
  final String? userId;
  @override
  final int? giftBagId;

  @override
  String toString() {
    return 'SendGiftResult(userId: $userId, giftBagId: $giftBagId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SendGiftResultImpl &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.giftBagId, giftBagId) ||
                other.giftBagId == giftBagId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userId, giftBagId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SendGiftResultImplCopyWith<_$SendGiftResultImpl> get copyWith =>
      __$$SendGiftResultImplCopyWithImpl<_$SendGiftResultImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SendGiftResultImplToJson(
      this,
    );
  }
}

abstract class _SendGiftResult implements SendGiftResult {
  const factory _SendGiftResult({final String? userId, final int? giftBagId}) =
      _$SendGiftResultImpl;

  factory _SendGiftResult.fromJson(Map<String, dynamic> json) =
      _$SendGiftResultImpl.fromJson;

  @override
  String? get userId;
  @override
  int? get giftBagId;
  @override
  @JsonKey(ignore: true)
  _$$SendGiftResultImplCopyWith<_$SendGiftResultImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
