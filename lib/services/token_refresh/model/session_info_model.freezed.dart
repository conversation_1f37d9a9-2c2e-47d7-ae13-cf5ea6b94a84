// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SessionInfoModel _$SessionInfoModelFromJson(Map<String, dynamic> json) {
  return _SessionInfoModel.fromJson(json);
}

/// @nodoc
mixin _$SessionInfoModel {
  String get accessToken => throw _privateConstructorUsedError;
  String get refreshToken => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionInfoModelCopyWith<SessionInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionInfoModelCopyWith<$Res> {
  factory $SessionInfoModelCopyWith(
          SessionInfoModel value, $Res Function(SessionInfoModel) then) =
      _$SessionInfoModelCopyWithImpl<$Res, SessionInfoModel>;
  @useResult
  $Res call({String accessToken, String refreshToken});
}

/// @nodoc
class _$SessionInfoModelCopyWithImpl<$Res, $Val extends SessionInfoModel>
    implements $SessionInfoModelCopyWith<$Res> {
  _$SessionInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = null,
  }) {
    return _then(_value.copyWith(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionInfoModelImplCopyWith<$Res>
    implements $SessionInfoModelCopyWith<$Res> {
  factory _$$SessionInfoModelImplCopyWith(_$SessionInfoModelImpl value,
          $Res Function(_$SessionInfoModelImpl) then) =
      __$$SessionInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String accessToken, String refreshToken});
}

/// @nodoc
class __$$SessionInfoModelImplCopyWithImpl<$Res>
    extends _$SessionInfoModelCopyWithImpl<$Res, _$SessionInfoModelImpl>
    implements _$$SessionInfoModelImplCopyWith<$Res> {
  __$$SessionInfoModelImplCopyWithImpl(_$SessionInfoModelImpl _value,
      $Res Function(_$SessionInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? accessToken = null,
    Object? refreshToken = null,
  }) {
    return _then(_$SessionInfoModelImpl(
      accessToken: null == accessToken
          ? _value.accessToken
          : accessToken // ignore: cast_nullable_to_non_nullable
              as String,
      refreshToken: null == refreshToken
          ? _value.refreshToken
          : refreshToken // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionInfoModelImpl implements _SessionInfoModel {
  const _$SessionInfoModelImpl(
      {required this.accessToken, required this.refreshToken});

  factory _$SessionInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionInfoModelImplFromJson(json);

  @override
  final String accessToken;
  @override
  final String refreshToken;

  @override
  String toString() {
    return 'SessionInfoModel(accessToken: $accessToken, refreshToken: $refreshToken)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionInfoModelImpl &&
            (identical(other.accessToken, accessToken) ||
                other.accessToken == accessToken) &&
            (identical(other.refreshToken, refreshToken) ||
                other.refreshToken == refreshToken));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, accessToken, refreshToken);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionInfoModelImplCopyWith<_$SessionInfoModelImpl> get copyWith =>
      __$$SessionInfoModelImplCopyWithImpl<_$SessionInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionInfoModelImplToJson(
      this,
    );
  }
}

abstract class _SessionInfoModel implements SessionInfoModel {
  const factory _SessionInfoModel(
      {required final String accessToken,
      required final String refreshToken}) = _$SessionInfoModelImpl;

  factory _SessionInfoModel.fromJson(Map<String, dynamic> json) =
      _$SessionInfoModelImpl.fromJson;

  @override
  String get accessToken;
  @override
  String get refreshToken;
  @override
  @JsonKey(ignore: true)
  _$$SessionInfoModelImplCopyWith<_$SessionInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
