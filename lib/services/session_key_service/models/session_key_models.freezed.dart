// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'session_key_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SessionKeyConfig _$SessionKeyConfigFromJson(Map<String, dynamic> json) {
  return _SessionKeyConfig.fromJson(json);
}

/// @nodoc
mixin _$SessionKeyConfig {
// Maximum number of messages that can be encrypted with a single key
  int get maxMessagesPerKey =>
      throw _privateConstructorUsedError; // Maximum age of a key before it needs to be rotated
  Duration get maxKeyAge =>
      throw _privateConstructorUsedError; // Grace period for using old keys during rotation
  Duration get gracePeriod => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionKeyConfigCopyWith<SessionKeyConfig> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionKeyConfigCopyWith<$Res> {
  factory $SessionKeyConfigCopyWith(
          SessionKeyConfig value, $Res Function(SessionKeyConfig) then) =
      _$SessionKeyConfigCopyWithImpl<$Res, SessionKeyConfig>;
  @useResult
  $Res call({int maxMessagesPerKey, Duration maxKeyAge, Duration gracePeriod});
}

/// @nodoc
class _$SessionKeyConfigCopyWithImpl<$Res, $Val extends SessionKeyConfig>
    implements $SessionKeyConfigCopyWith<$Res> {
  _$SessionKeyConfigCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxMessagesPerKey = null,
    Object? maxKeyAge = null,
    Object? gracePeriod = null,
  }) {
    return _then(_value.copyWith(
      maxMessagesPerKey: null == maxMessagesPerKey
          ? _value.maxMessagesPerKey
          : maxMessagesPerKey // ignore: cast_nullable_to_non_nullable
              as int,
      maxKeyAge: null == maxKeyAge
          ? _value.maxKeyAge
          : maxKeyAge // ignore: cast_nullable_to_non_nullable
              as Duration,
      gracePeriod: null == gracePeriod
          ? _value.gracePeriod
          : gracePeriod // ignore: cast_nullable_to_non_nullable
              as Duration,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionKeyConfigImplCopyWith<$Res>
    implements $SessionKeyConfigCopyWith<$Res> {
  factory _$$SessionKeyConfigImplCopyWith(_$SessionKeyConfigImpl value,
          $Res Function(_$SessionKeyConfigImpl) then) =
      __$$SessionKeyConfigImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int maxMessagesPerKey, Duration maxKeyAge, Duration gracePeriod});
}

/// @nodoc
class __$$SessionKeyConfigImplCopyWithImpl<$Res>
    extends _$SessionKeyConfigCopyWithImpl<$Res, _$SessionKeyConfigImpl>
    implements _$$SessionKeyConfigImplCopyWith<$Res> {
  __$$SessionKeyConfigImplCopyWithImpl(_$SessionKeyConfigImpl _value,
      $Res Function(_$SessionKeyConfigImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? maxMessagesPerKey = null,
    Object? maxKeyAge = null,
    Object? gracePeriod = null,
  }) {
    return _then(_$SessionKeyConfigImpl(
      maxMessagesPerKey: null == maxMessagesPerKey
          ? _value.maxMessagesPerKey
          : maxMessagesPerKey // ignore: cast_nullable_to_non_nullable
              as int,
      maxKeyAge: null == maxKeyAge
          ? _value.maxKeyAge
          : maxKeyAge // ignore: cast_nullable_to_non_nullable
              as Duration,
      gracePeriod: null == gracePeriod
          ? _value.gracePeriod
          : gracePeriod // ignore: cast_nullable_to_non_nullable
              as Duration,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionKeyConfigImpl implements _SessionKeyConfig {
  const _$SessionKeyConfigImpl(
      {this.maxMessagesPerKey = 100,
      this.maxKeyAge = const Duration(hours: 24),
      this.gracePeriod = const Duration(minutes: 10)});

  factory _$SessionKeyConfigImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionKeyConfigImplFromJson(json);

// Maximum number of messages that can be encrypted with a single key
  @override
  @JsonKey()
  final int maxMessagesPerKey;
// Maximum age of a key before it needs to be rotated
  @override
  @JsonKey()
  final Duration maxKeyAge;
// Grace period for using old keys during rotation
  @override
  @JsonKey()
  final Duration gracePeriod;

  @override
  String toString() {
    return 'SessionKeyConfig(maxMessagesPerKey: $maxMessagesPerKey, maxKeyAge: $maxKeyAge, gracePeriod: $gracePeriod)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionKeyConfigImpl &&
            (identical(other.maxMessagesPerKey, maxMessagesPerKey) ||
                other.maxMessagesPerKey == maxMessagesPerKey) &&
            (identical(other.maxKeyAge, maxKeyAge) ||
                other.maxKeyAge == maxKeyAge) &&
            (identical(other.gracePeriod, gracePeriod) ||
                other.gracePeriod == gracePeriod));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, maxMessagesPerKey, maxKeyAge, gracePeriod);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionKeyConfigImplCopyWith<_$SessionKeyConfigImpl> get copyWith =>
      __$$SessionKeyConfigImplCopyWithImpl<_$SessionKeyConfigImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionKeyConfigImplToJson(
      this,
    );
  }
}

abstract class _SessionKeyConfig implements SessionKeyConfig {
  const factory _SessionKeyConfig(
      {final int maxMessagesPerKey,
      final Duration maxKeyAge,
      final Duration gracePeriod}) = _$SessionKeyConfigImpl;

  factory _SessionKeyConfig.fromJson(Map<String, dynamic> json) =
      _$SessionKeyConfigImpl.fromJson;

  @override // Maximum number of messages that can be encrypted with a single key
  int get maxMessagesPerKey;
  @override // Maximum age of a key before it needs to be rotated
  Duration get maxKeyAge;
  @override // Grace period for using old keys during rotation
  Duration get gracePeriod;
  @override
  @JsonKey(ignore: true)
  _$$SessionKeyConfigImplCopyWith<_$SessionKeyConfigImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

HistoricalSessionKey _$HistoricalSessionKeyFromJson(Map<String, dynamic> json) {
  return _HistoricalSessionKey.fromJson(json);
}

/// @nodoc
mixin _$HistoricalSessionKey {
  String get sessionKey => throw _privateConstructorUsedError;
  int get version => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;
  Map<String, String> get encryptedKeys => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $HistoricalSessionKeyCopyWith<HistoricalSessionKey> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $HistoricalSessionKeyCopyWith<$Res> {
  factory $HistoricalSessionKeyCopyWith(HistoricalSessionKey value,
          $Res Function(HistoricalSessionKey) then) =
      _$HistoricalSessionKeyCopyWithImpl<$Res, HistoricalSessionKey>;
  @useResult
  $Res call(
      {String sessionKey,
      int version,
      DateTime createdAt,
      DateTime expiresAt,
      String createdBy,
      Map<String, String> encryptedKeys});
}

/// @nodoc
class _$HistoricalSessionKeyCopyWithImpl<$Res,
        $Val extends HistoricalSessionKey>
    implements $HistoricalSessionKeyCopyWith<$Res> {
  _$HistoricalSessionKeyCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionKey = null,
    Object? version = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? createdBy = null,
    Object? encryptedKeys = null,
  }) {
    return _then(_value.copyWith(
      sessionKey: null == sessionKey
          ? _value.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      encryptedKeys: null == encryptedKeys
          ? _value.encryptedKeys
          : encryptedKeys // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$HistoricalSessionKeyImplCopyWith<$Res>
    implements $HistoricalSessionKeyCopyWith<$Res> {
  factory _$$HistoricalSessionKeyImplCopyWith(_$HistoricalSessionKeyImpl value,
          $Res Function(_$HistoricalSessionKeyImpl) then) =
      __$$HistoricalSessionKeyImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String sessionKey,
      int version,
      DateTime createdAt,
      DateTime expiresAt,
      String createdBy,
      Map<String, String> encryptedKeys});
}

/// @nodoc
class __$$HistoricalSessionKeyImplCopyWithImpl<$Res>
    extends _$HistoricalSessionKeyCopyWithImpl<$Res, _$HistoricalSessionKeyImpl>
    implements _$$HistoricalSessionKeyImplCopyWith<$Res> {
  __$$HistoricalSessionKeyImplCopyWithImpl(_$HistoricalSessionKeyImpl _value,
      $Res Function(_$HistoricalSessionKeyImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionKey = null,
    Object? version = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? createdBy = null,
    Object? encryptedKeys = null,
  }) {
    return _then(_$HistoricalSessionKeyImpl(
      sessionKey: null == sessionKey
          ? _value.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      encryptedKeys: null == encryptedKeys
          ? _value._encryptedKeys
          : encryptedKeys // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$HistoricalSessionKeyImpl implements _HistoricalSessionKey {
  const _$HistoricalSessionKeyImpl(
      {required this.sessionKey,
      required this.version,
      required this.createdAt,
      required this.expiresAt,
      required this.createdBy,
      required final Map<String, String> encryptedKeys})
      : _encryptedKeys = encryptedKeys;

  factory _$HistoricalSessionKeyImpl.fromJson(Map<String, dynamic> json) =>
      _$$HistoricalSessionKeyImplFromJson(json);

  @override
  final String sessionKey;
  @override
  final int version;
  @override
  final DateTime createdAt;
  @override
  final DateTime expiresAt;
  @override
  final String createdBy;
  final Map<String, String> _encryptedKeys;
  @override
  Map<String, String> get encryptedKeys {
    if (_encryptedKeys is EqualUnmodifiableMapView) return _encryptedKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_encryptedKeys);
  }

  @override
  String toString() {
    return 'HistoricalSessionKey(sessionKey: $sessionKey, version: $version, createdAt: $createdAt, expiresAt: $expiresAt, createdBy: $createdBy, encryptedKeys: $encryptedKeys)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$HistoricalSessionKeyImpl &&
            (identical(other.sessionKey, sessionKey) ||
                other.sessionKey == sessionKey) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            const DeepCollectionEquality()
                .equals(other._encryptedKeys, _encryptedKeys));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionKey,
      version,
      createdAt,
      expiresAt,
      createdBy,
      const DeepCollectionEquality().hash(_encryptedKeys));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$HistoricalSessionKeyImplCopyWith<_$HistoricalSessionKeyImpl>
      get copyWith =>
          __$$HistoricalSessionKeyImplCopyWithImpl<_$HistoricalSessionKeyImpl>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$HistoricalSessionKeyImplToJson(
      this,
    );
  }
}

abstract class _HistoricalSessionKey implements HistoricalSessionKey {
  const factory _HistoricalSessionKey(
          {required final String sessionKey,
          required final int version,
          required final DateTime createdAt,
          required final DateTime expiresAt,
          required final String createdBy,
          required final Map<String, String> encryptedKeys}) =
      _$HistoricalSessionKeyImpl;

  factory _HistoricalSessionKey.fromJson(Map<String, dynamic> json) =
      _$HistoricalSessionKeyImpl.fromJson;

  @override
  String get sessionKey;
  @override
  int get version;
  @override
  DateTime get createdAt;
  @override
  DateTime get expiresAt;
  @override
  String get createdBy;
  @override
  Map<String, String> get encryptedKeys;
  @override
  @JsonKey(ignore: true)
  _$$HistoricalSessionKeyImplCopyWith<_$HistoricalSessionKeyImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SessionKeyMetadata _$SessionKeyMetadataFromJson(Map<String, dynamic> json) {
  return _SessionKeyMetadata.fromJson(json);
}

/// @nodoc
mixin _$SessionKeyMetadata {
  String get sessionKey => throw _privateConstructorUsedError;
  String get sessionId => throw _privateConstructorUsedError;
  int get version => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;
  Map<String, String> get encryptedKeys => throw _privateConstructorUsedError;
  int get messageCount => throw _privateConstructorUsedError;
  Map<int, HistoricalSessionKey> get historicalKeys =>
      throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionKeyMetadataCopyWith<SessionKeyMetadata> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionKeyMetadataCopyWith<$Res> {
  factory $SessionKeyMetadataCopyWith(
          SessionKeyMetadata value, $Res Function(SessionKeyMetadata) then) =
      _$SessionKeyMetadataCopyWithImpl<$Res, SessionKeyMetadata>;
  @useResult
  $Res call(
      {String sessionKey,
      String sessionId,
      int version,
      DateTime createdAt,
      DateTime expiresAt,
      String createdBy,
      Map<String, String> encryptedKeys,
      int messageCount,
      Map<int, HistoricalSessionKey> historicalKeys});
}

/// @nodoc
class _$SessionKeyMetadataCopyWithImpl<$Res, $Val extends SessionKeyMetadata>
    implements $SessionKeyMetadataCopyWith<$Res> {
  _$SessionKeyMetadataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionKey = null,
    Object? sessionId = null,
    Object? version = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? createdBy = null,
    Object? encryptedKeys = null,
    Object? messageCount = null,
    Object? historicalKeys = null,
  }) {
    return _then(_value.copyWith(
      sessionKey: null == sessionKey
          ? _value.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      encryptedKeys: null == encryptedKeys
          ? _value.encryptedKeys
          : encryptedKeys // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      messageCount: null == messageCount
          ? _value.messageCount
          : messageCount // ignore: cast_nullable_to_non_nullable
              as int,
      historicalKeys: null == historicalKeys
          ? _value.historicalKeys
          : historicalKeys // ignore: cast_nullable_to_non_nullable
              as Map<int, HistoricalSessionKey>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionKeyMetadataImplCopyWith<$Res>
    implements $SessionKeyMetadataCopyWith<$Res> {
  factory _$$SessionKeyMetadataImplCopyWith(_$SessionKeyMetadataImpl value,
          $Res Function(_$SessionKeyMetadataImpl) then) =
      __$$SessionKeyMetadataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String sessionKey,
      String sessionId,
      int version,
      DateTime createdAt,
      DateTime expiresAt,
      String createdBy,
      Map<String, String> encryptedKeys,
      int messageCount,
      Map<int, HistoricalSessionKey> historicalKeys});
}

/// @nodoc
class __$$SessionKeyMetadataImplCopyWithImpl<$Res>
    extends _$SessionKeyMetadataCopyWithImpl<$Res, _$SessionKeyMetadataImpl>
    implements _$$SessionKeyMetadataImplCopyWith<$Res> {
  __$$SessionKeyMetadataImplCopyWithImpl(_$SessionKeyMetadataImpl _value,
      $Res Function(_$SessionKeyMetadataImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionKey = null,
    Object? sessionId = null,
    Object? version = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? createdBy = null,
    Object? encryptedKeys = null,
    Object? messageCount = null,
    Object? historicalKeys = null,
  }) {
    return _then(_$SessionKeyMetadataImpl(
      sessionKey: null == sessionKey
          ? _value.sessionKey
          : sessionKey // ignore: cast_nullable_to_non_nullable
              as String,
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      version: null == version
          ? _value.version
          : version // ignore: cast_nullable_to_non_nullable
              as int,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
      encryptedKeys: null == encryptedKeys
          ? _value._encryptedKeys
          : encryptedKeys // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      messageCount: null == messageCount
          ? _value.messageCount
          : messageCount // ignore: cast_nullable_to_non_nullable
              as int,
      historicalKeys: null == historicalKeys
          ? _value._historicalKeys
          : historicalKeys // ignore: cast_nullable_to_non_nullable
              as Map<int, HistoricalSessionKey>,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionKeyMetadataImpl implements _SessionKeyMetadata {
  const _$SessionKeyMetadataImpl(
      {required this.sessionKey,
      required this.sessionId,
      required this.version,
      required this.createdAt,
      required this.expiresAt,
      required this.createdBy,
      required final Map<String, String> encryptedKeys,
      this.messageCount = 0,
      final Map<int, HistoricalSessionKey> historicalKeys = const {}})
      : _encryptedKeys = encryptedKeys,
        _historicalKeys = historicalKeys;

  factory _$SessionKeyMetadataImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionKeyMetadataImplFromJson(json);

  @override
  final String sessionKey;
  @override
  final String sessionId;
  @override
  final int version;
  @override
  final DateTime createdAt;
  @override
  final DateTime expiresAt;
  @override
  final String createdBy;
  final Map<String, String> _encryptedKeys;
  @override
  Map<String, String> get encryptedKeys {
    if (_encryptedKeys is EqualUnmodifiableMapView) return _encryptedKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_encryptedKeys);
  }

  @override
  @JsonKey()
  final int messageCount;
  final Map<int, HistoricalSessionKey> _historicalKeys;
  @override
  @JsonKey()
  Map<int, HistoricalSessionKey> get historicalKeys {
    if (_historicalKeys is EqualUnmodifiableMapView) return _historicalKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_historicalKeys);
  }

  @override
  String toString() {
    return 'SessionKeyMetadata(sessionKey: $sessionKey, sessionId: $sessionId, version: $version, createdAt: $createdAt, expiresAt: $expiresAt, createdBy: $createdBy, encryptedKeys: $encryptedKeys, messageCount: $messageCount, historicalKeys: $historicalKeys)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionKeyMetadataImpl &&
            (identical(other.sessionKey, sessionKey) ||
                other.sessionKey == sessionKey) &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.version, version) || other.version == version) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy) &&
            const DeepCollectionEquality()
                .equals(other._encryptedKeys, _encryptedKeys) &&
            (identical(other.messageCount, messageCount) ||
                other.messageCount == messageCount) &&
            const DeepCollectionEquality()
                .equals(other._historicalKeys, _historicalKeys));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionKey,
      sessionId,
      version,
      createdAt,
      expiresAt,
      createdBy,
      const DeepCollectionEquality().hash(_encryptedKeys),
      messageCount,
      const DeepCollectionEquality().hash(_historicalKeys));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionKeyMetadataImplCopyWith<_$SessionKeyMetadataImpl> get copyWith =>
      __$$SessionKeyMetadataImplCopyWithImpl<_$SessionKeyMetadataImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionKeyMetadataImplToJson(
      this,
    );
  }
}

abstract class _SessionKeyMetadata implements SessionKeyMetadata {
  const factory _SessionKeyMetadata(
          {required final String sessionKey,
          required final String sessionId,
          required final int version,
          required final DateTime createdAt,
          required final DateTime expiresAt,
          required final String createdBy,
          required final Map<String, String> encryptedKeys,
          final int messageCount,
          final Map<int, HistoricalSessionKey> historicalKeys}) =
      _$SessionKeyMetadataImpl;

  factory _SessionKeyMetadata.fromJson(Map<String, dynamic> json) =
      _$SessionKeyMetadataImpl.fromJson;

  @override
  String get sessionKey;
  @override
  String get sessionId;
  @override
  int get version;
  @override
  DateTime get createdAt;
  @override
  DateTime get expiresAt;
  @override
  String get createdBy;
  @override
  Map<String, String> get encryptedKeys;
  @override
  int get messageCount;
  @override
  Map<int, HistoricalSessionKey> get historicalKeys;
  @override
  @JsonKey(ignore: true)
  _$$SessionKeyMetadataImplCopyWith<_$SessionKeyMetadataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

SessionKeyRotationRequest _$SessionKeyRotationRequestFromJson(
    Map<String, dynamic> json) {
  return _SessionKeyRotationRequest.fromJson(json);
}

/// @nodoc
mixin _$SessionKeyRotationRequest {
  String get sessionId => throw _privateConstructorUsedError;
  int get currentVersion => throw _privateConstructorUsedError;
  String get requestedBy => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionKeyRotationRequestCopyWith<SessionKeyRotationRequest> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionKeyRotationRequestCopyWith<$Res> {
  factory $SessionKeyRotationRequestCopyWith(SessionKeyRotationRequest value,
          $Res Function(SessionKeyRotationRequest) then) =
      _$SessionKeyRotationRequestCopyWithImpl<$Res, SessionKeyRotationRequest>;
  @useResult
  $Res call({String sessionId, int currentVersion, String requestedBy});
}

/// @nodoc
class _$SessionKeyRotationRequestCopyWithImpl<$Res,
        $Val extends SessionKeyRotationRequest>
    implements $SessionKeyRotationRequestCopyWith<$Res> {
  _$SessionKeyRotationRequestCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? currentVersion = null,
    Object? requestedBy = null,
  }) {
    return _then(_value.copyWith(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      currentVersion: null == currentVersion
          ? _value.currentVersion
          : currentVersion // ignore: cast_nullable_to_non_nullable
              as int,
      requestedBy: null == requestedBy
          ? _value.requestedBy
          : requestedBy // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionKeyRotationRequestImplCopyWith<$Res>
    implements $SessionKeyRotationRequestCopyWith<$Res> {
  factory _$$SessionKeyRotationRequestImplCopyWith(
          _$SessionKeyRotationRequestImpl value,
          $Res Function(_$SessionKeyRotationRequestImpl) then) =
      __$$SessionKeyRotationRequestImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({String sessionId, int currentVersion, String requestedBy});
}

/// @nodoc
class __$$SessionKeyRotationRequestImplCopyWithImpl<$Res>
    extends _$SessionKeyRotationRequestCopyWithImpl<$Res,
        _$SessionKeyRotationRequestImpl>
    implements _$$SessionKeyRotationRequestImplCopyWith<$Res> {
  __$$SessionKeyRotationRequestImplCopyWithImpl(
      _$SessionKeyRotationRequestImpl _value,
      $Res Function(_$SessionKeyRotationRequestImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? currentVersion = null,
    Object? requestedBy = null,
  }) {
    return _then(_$SessionKeyRotationRequestImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      currentVersion: null == currentVersion
          ? _value.currentVersion
          : currentVersion // ignore: cast_nullable_to_non_nullable
              as int,
      requestedBy: null == requestedBy
          ? _value.requestedBy
          : requestedBy // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SessionKeyRotationRequestImpl implements _SessionKeyRotationRequest {
  const _$SessionKeyRotationRequestImpl(
      {required this.sessionId,
      required this.currentVersion,
      required this.requestedBy});

  factory _$SessionKeyRotationRequestImpl.fromJson(Map<String, dynamic> json) =>
      _$$SessionKeyRotationRequestImplFromJson(json);

  @override
  final String sessionId;
  @override
  final int currentVersion;
  @override
  final String requestedBy;

  @override
  String toString() {
    return 'SessionKeyRotationRequest(sessionId: $sessionId, currentVersion: $currentVersion, requestedBy: $requestedBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionKeyRotationRequestImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.currentVersion, currentVersion) ||
                other.currentVersion == currentVersion) &&
            (identical(other.requestedBy, requestedBy) ||
                other.requestedBy == requestedBy));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, sessionId, currentVersion, requestedBy);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionKeyRotationRequestImplCopyWith<_$SessionKeyRotationRequestImpl>
      get copyWith => __$$SessionKeyRotationRequestImplCopyWithImpl<
          _$SessionKeyRotationRequestImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionKeyRotationRequestImplToJson(
      this,
    );
  }
}

abstract class _SessionKeyRotationRequest implements SessionKeyRotationRequest {
  const factory _SessionKeyRotationRequest(
      {required final String sessionId,
      required final int currentVersion,
      required final String requestedBy}) = _$SessionKeyRotationRequestImpl;

  factory _SessionKeyRotationRequest.fromJson(Map<String, dynamic> json) =
      _$SessionKeyRotationRequestImpl.fromJson;

  @override
  String get sessionId;
  @override
  int get currentVersion;
  @override
  String get requestedBy;
  @override
  @JsonKey(ignore: true)
  _$$SessionKeyRotationRequestImplCopyWith<_$SessionKeyRotationRequestImpl>
      get copyWith => throw _privateConstructorUsedError;
}

SessionKeyRotationResponse _$SessionKeyRotationResponseFromJson(
    Map<String, dynamic> json) {
  return _SessionKeyRotationResponse.fromJson(json);
}

/// @nodoc
mixin _$SessionKeyRotationResponse {
  String get sessionId => throw _privateConstructorUsedError;
  int get newVersion => throw _privateConstructorUsedError;
  Map<String, String> get encryptedKeys => throw _privateConstructorUsedError;
  DateTime get createdAt => throw _privateConstructorUsedError;
  DateTime get expiresAt => throw _privateConstructorUsedError;
  String get createdBy => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SessionKeyRotationResponseCopyWith<SessionKeyRotationResponse>
      get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SessionKeyRotationResponseCopyWith<$Res> {
  factory $SessionKeyRotationResponseCopyWith(SessionKeyRotationResponse value,
          $Res Function(SessionKeyRotationResponse) then) =
      _$SessionKeyRotationResponseCopyWithImpl<$Res,
          SessionKeyRotationResponse>;
  @useResult
  $Res call(
      {String sessionId,
      int newVersion,
      Map<String, String> encryptedKeys,
      DateTime createdAt,
      DateTime expiresAt,
      String createdBy});
}

/// @nodoc
class _$SessionKeyRotationResponseCopyWithImpl<$Res,
        $Val extends SessionKeyRotationResponse>
    implements $SessionKeyRotationResponseCopyWith<$Res> {
  _$SessionKeyRotationResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? newVersion = null,
    Object? encryptedKeys = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? createdBy = null,
  }) {
    return _then(_value.copyWith(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      newVersion: null == newVersion
          ? _value.newVersion
          : newVersion // ignore: cast_nullable_to_non_nullable
              as int,
      encryptedKeys: null == encryptedKeys
          ? _value.encryptedKeys
          : encryptedKeys // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SessionKeyRotationResponseImplCopyWith<$Res>
    implements $SessionKeyRotationResponseCopyWith<$Res> {
  factory _$$SessionKeyRotationResponseImplCopyWith(
          _$SessionKeyRotationResponseImpl value,
          $Res Function(_$SessionKeyRotationResponseImpl) then) =
      __$$SessionKeyRotationResponseImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String sessionId,
      int newVersion,
      Map<String, String> encryptedKeys,
      DateTime createdAt,
      DateTime expiresAt,
      String createdBy});
}

/// @nodoc
class __$$SessionKeyRotationResponseImplCopyWithImpl<$Res>
    extends _$SessionKeyRotationResponseCopyWithImpl<$Res,
        _$SessionKeyRotationResponseImpl>
    implements _$$SessionKeyRotationResponseImplCopyWith<$Res> {
  __$$SessionKeyRotationResponseImplCopyWithImpl(
      _$SessionKeyRotationResponseImpl _value,
      $Res Function(_$SessionKeyRotationResponseImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? sessionId = null,
    Object? newVersion = null,
    Object? encryptedKeys = null,
    Object? createdAt = null,
    Object? expiresAt = null,
    Object? createdBy = null,
  }) {
    return _then(_$SessionKeyRotationResponseImpl(
      sessionId: null == sessionId
          ? _value.sessionId
          : sessionId // ignore: cast_nullable_to_non_nullable
              as String,
      newVersion: null == newVersion
          ? _value.newVersion
          : newVersion // ignore: cast_nullable_to_non_nullable
              as int,
      encryptedKeys: null == encryptedKeys
          ? _value._encryptedKeys
          : encryptedKeys // ignore: cast_nullable_to_non_nullable
              as Map<String, String>,
      createdAt: null == createdAt
          ? _value.createdAt
          : createdAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      expiresAt: null == expiresAt
          ? _value.expiresAt
          : expiresAt // ignore: cast_nullable_to_non_nullable
              as DateTime,
      createdBy: null == createdBy
          ? _value.createdBy
          : createdBy // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$SessionKeyRotationResponseImpl extends _SessionKeyRotationResponse {
  const _$SessionKeyRotationResponseImpl(
      {required this.sessionId,
      required this.newVersion,
      required final Map<String, String> encryptedKeys,
      required this.createdAt,
      required this.expiresAt,
      required this.createdBy})
      : _encryptedKeys = encryptedKeys,
        super._();

  factory _$SessionKeyRotationResponseImpl.fromJson(
          Map<String, dynamic> json) =>
      _$$SessionKeyRotationResponseImplFromJson(json);

  @override
  final String sessionId;
  @override
  final int newVersion;
  final Map<String, String> _encryptedKeys;
  @override
  Map<String, String> get encryptedKeys {
    if (_encryptedKeys is EqualUnmodifiableMapView) return _encryptedKeys;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableMapView(_encryptedKeys);
  }

  @override
  final DateTime createdAt;
  @override
  final DateTime expiresAt;
  @override
  final String createdBy;

  @override
  String toString() {
    return 'SessionKeyRotationResponse(sessionId: $sessionId, newVersion: $newVersion, encryptedKeys: $encryptedKeys, createdAt: $createdAt, expiresAt: $expiresAt, createdBy: $createdBy)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SessionKeyRotationResponseImpl &&
            (identical(other.sessionId, sessionId) ||
                other.sessionId == sessionId) &&
            (identical(other.newVersion, newVersion) ||
                other.newVersion == newVersion) &&
            const DeepCollectionEquality()
                .equals(other._encryptedKeys, _encryptedKeys) &&
            (identical(other.createdAt, createdAt) ||
                other.createdAt == createdAt) &&
            (identical(other.expiresAt, expiresAt) ||
                other.expiresAt == expiresAt) &&
            (identical(other.createdBy, createdBy) ||
                other.createdBy == createdBy));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      sessionId,
      newVersion,
      const DeepCollectionEquality().hash(_encryptedKeys),
      createdAt,
      expiresAt,
      createdBy);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SessionKeyRotationResponseImplCopyWith<_$SessionKeyRotationResponseImpl>
      get copyWith => __$$SessionKeyRotationResponseImplCopyWithImpl<
          _$SessionKeyRotationResponseImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SessionKeyRotationResponseImplToJson(
      this,
    );
  }
}

abstract class _SessionKeyRotationResponse extends SessionKeyRotationResponse {
  const factory _SessionKeyRotationResponse(
      {required final String sessionId,
      required final int newVersion,
      required final Map<String, String> encryptedKeys,
      required final DateTime createdAt,
      required final DateTime expiresAt,
      required final String createdBy}) = _$SessionKeyRotationResponseImpl;
  const _SessionKeyRotationResponse._() : super._();

  factory _SessionKeyRotationResponse.fromJson(Map<String, dynamic> json) =
      _$SessionKeyRotationResponseImpl.fromJson;

  @override
  String get sessionId;
  @override
  int get newVersion;
  @override
  Map<String, String> get encryptedKeys;
  @override
  DateTime get createdAt;
  @override
  DateTime get expiresAt;
  @override
  String get createdBy;
  @override
  @JsonKey(ignore: true)
  _$$SessionKeyRotationResponseImplCopyWith<_$SessionKeyRotationResponseImpl>
      get copyWith => throw _privateConstructorUsedError;
}
