import 'dart:convert';

import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/domain/models/conversation_model.dart';
import 'package:isar/isar.dart';

part 'isar_conversation_service.g.dart';

@Collection()
class IsarConversation {
  late int id;

  @Index()
  late String conversationId;

  @Index()
  late String userId;

  late String jsonData;

  @Index()
  late int updatedAt;

  @Index()
  late bool pinned;
}

class IsarConversationService {
  final Isar _isar;

  IsarConversationService(this._isar);

  Future<List<ConversationModel>> queryConversations({
    required String userId,
    int limit = 20,
    int offset = 0,
    bool? isPinned,
  }) async {
    final query = _isar.isarConversations.where().userIdEqualTo(userId);

    if (isPinned != null) {
      query.pinnedEqualTo(isPinned);
    }

    final conversations = query
        .sortByPinnedDesc()
        .thenByUpdatedAtDesc()
        .findAll(offset: offset, limit: limit);

    LogUtils.d(
        'Query conversations: ${conversations.map((e) => e.conversationId)}',
        tag: 'IsarConversationService');

    return conversations
        .map((e) => ConversationModel.fromJson(json.decode(e.jsonData)))
        .toList();
  }

  Future<ConversationModel?> getConversation(
    String conversationId,
    String userId,
  ) async {
    final conversation = _isar.isarConversations
        .where()
        .conversationIdEqualTo(conversationId)
        .userIdEqualTo(userId)
        .findFirst();

    return conversation != null
        ? ConversationModel.fromJson(json.decode(conversation.jsonData))
        : null;
  }

  Future<void> createOrUpdateConversation(
    ConversationModel conversation, {
    required String userId,
  }) async {
    await _isar.writeAsync((isar) {
      final existing = isar.isarConversations
          .where()
          .conversationIdEqualTo(conversation.id)
          .userIdEqualTo(userId)
          .findFirst();

      final isarConversation = existing ?? IsarConversation()
        ..id = existing?.id ?? isar.isarConversations.autoIncrement();

      isarConversation
        ..conversationId = conversation.id
        ..userId = userId
        ..jsonData = json.encode(conversation.toJson())
        ..updatedAt = conversation.updatedAt
        ..pinned = conversation.pinned;

      return isar.isarConversations.put(isarConversation);
    });
  }

  Future<void> deleteConversation(
    String conversationId,
    String userId,
  ) async {
    await _isar.writeAsync((isar) {
      return isar.isarConversations
          .where()
          .conversationIdEqualTo(conversationId)
          .userIdEqualTo(userId)
          .deleteAll();
    });
  }

  Future<void> deleteAllConversations(String userId) async {
    await _isar.writeAsync((isar) {
      return isar.isarConversations.where().userIdEqualTo(userId).deleteAll();
    });
  }
}
