// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'signal_key_key_bundle.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SignalKeyKeyBundle _$SignalKeyKeyBundleFromJson(Map<String, dynamic> json) {
  return _SignalKeyKeyBundle.fromJson(json);
}

/// @nodoc
mixin _$SignalKeyKeyBundle {
  String get identityKey => throw _privateConstructorUsedError;
  String get signedPreKey => throw _privateConstructorUsedError;
  String? get preKey => throw _privateConstructorUsedError;
  int get registrationId => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SignalKeyKeyBundleCopyWith<SignalKeyKeyBundle> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SignalKeyKeyBundleCopyWith<$Res> {
  factory $SignalKeyKeyBundleCopyWith(
          SignalKeyKeyBundle value, $Res Function(SignalKeyKeyBundle) then) =
      _$SignalKeyKeyBundleCopyWithImpl<$Res, SignalKeyKeyBundle>;
  @useResult
  $Res call(
      {String identityKey,
      String signedPreKey,
      String? preKey,
      int registrationId});
}

/// @nodoc
class _$SignalKeyKeyBundleCopyWithImpl<$Res, $Val extends SignalKeyKeyBundle>
    implements $SignalKeyKeyBundleCopyWith<$Res> {
  _$SignalKeyKeyBundleCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identityKey = null,
    Object? signedPreKey = null,
    Object? preKey = freezed,
    Object? registrationId = null,
  }) {
    return _then(_value.copyWith(
      identityKey: null == identityKey
          ? _value.identityKey
          : identityKey // ignore: cast_nullable_to_non_nullable
              as String,
      signedPreKey: null == signedPreKey
          ? _value.signedPreKey
          : signedPreKey // ignore: cast_nullable_to_non_nullable
              as String,
      preKey: freezed == preKey
          ? _value.preKey
          : preKey // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationId: null == registrationId
          ? _value.registrationId
          : registrationId // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SignalKeyKeyBundleImplCopyWith<$Res>
    implements $SignalKeyKeyBundleCopyWith<$Res> {
  factory _$$SignalKeyKeyBundleImplCopyWith(_$SignalKeyKeyBundleImpl value,
          $Res Function(_$SignalKeyKeyBundleImpl) then) =
      __$$SignalKeyKeyBundleImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String identityKey,
      String signedPreKey,
      String? preKey,
      int registrationId});
}

/// @nodoc
class __$$SignalKeyKeyBundleImplCopyWithImpl<$Res>
    extends _$SignalKeyKeyBundleCopyWithImpl<$Res, _$SignalKeyKeyBundleImpl>
    implements _$$SignalKeyKeyBundleImplCopyWith<$Res> {
  __$$SignalKeyKeyBundleImplCopyWithImpl(_$SignalKeyKeyBundleImpl _value,
      $Res Function(_$SignalKeyKeyBundleImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? identityKey = null,
    Object? signedPreKey = null,
    Object? preKey = freezed,
    Object? registrationId = null,
  }) {
    return _then(_$SignalKeyKeyBundleImpl(
      identityKey: null == identityKey
          ? _value.identityKey
          : identityKey // ignore: cast_nullable_to_non_nullable
              as String,
      signedPreKey: null == signedPreKey
          ? _value.signedPreKey
          : signedPreKey // ignore: cast_nullable_to_non_nullable
              as String,
      preKey: freezed == preKey
          ? _value.preKey
          : preKey // ignore: cast_nullable_to_non_nullable
              as String?,
      registrationId: null == registrationId
          ? _value.registrationId
          : registrationId // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$SignalKeyKeyBundleImpl implements _SignalKeyKeyBundle {
  const _$SignalKeyKeyBundleImpl(
      {required this.identityKey,
      required this.signedPreKey,
      this.preKey,
      required this.registrationId});

  factory _$SignalKeyKeyBundleImpl.fromJson(Map<String, dynamic> json) =>
      _$$SignalKeyKeyBundleImplFromJson(json);

  @override
  final String identityKey;
  @override
  final String signedPreKey;
  @override
  final String? preKey;
  @override
  final int registrationId;

  @override
  String toString() {
    return 'SignalKeyKeyBundle(identityKey: $identityKey, signedPreKey: $signedPreKey, preKey: $preKey, registrationId: $registrationId)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SignalKeyKeyBundleImpl &&
            (identical(other.identityKey, identityKey) ||
                other.identityKey == identityKey) &&
            (identical(other.signedPreKey, signedPreKey) ||
                other.signedPreKey == signedPreKey) &&
            (identical(other.preKey, preKey) || other.preKey == preKey) &&
            (identical(other.registrationId, registrationId) ||
                other.registrationId == registrationId));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType, identityKey, signedPreKey, preKey, registrationId);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SignalKeyKeyBundleImplCopyWith<_$SignalKeyKeyBundleImpl> get copyWith =>
      __$$SignalKeyKeyBundleImplCopyWithImpl<_$SignalKeyKeyBundleImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SignalKeyKeyBundleImplToJson(
      this,
    );
  }
}

abstract class _SignalKeyKeyBundle implements SignalKeyKeyBundle {
  const factory _SignalKeyKeyBundle(
      {required final String identityKey,
      required final String signedPreKey,
      final String? preKey,
      required final int registrationId}) = _$SignalKeyKeyBundleImpl;

  factory _SignalKeyKeyBundle.fromJson(Map<String, dynamic> json) =
      _$SignalKeyKeyBundleImpl.fromJson;

  @override
  String get identityKey;
  @override
  String get signedPreKey;
  @override
  String? get preKey;
  @override
  int get registrationId;
  @override
  @JsonKey(ignore: true)
  _$$SignalKeyKeyBundleImplCopyWith<_$SignalKeyKeyBundleImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
