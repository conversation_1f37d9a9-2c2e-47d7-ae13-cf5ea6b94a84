// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'avatar_frame_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

AvatarFrameModel _$AvatarFrameModelFromJson(Map<String, dynamic> json) {
  return _AvatarFrameModel.fromJson(json);
}

/// @nodoc
mixin _$AvatarFrameModel {
  int? get id => throw _privateConstructorUsedError;
  String? get userId => throw _privateConstructorUsedError;
  int? get giftId => throw _privateConstructorUsedError;
  String? get source => throw _privateConstructorUsedError;
  String? get giftStatus => throw _privateConstructorUsedError;
  DateTime? get activateExpireTime => throw _privateConstructorUsedError;
  DateTime? get createdTime => throw _privateConstructorUsedError;
  DateTime? get expireTime => throw _privateConstructorUsedError;
  String? get name => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  String? get svgaUrl => throw _privateConstructorUsedError;
  int? get price => throw _privateConstructorUsedError;
  String? get giftFormat => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $AvatarFrameModelCopyWith<AvatarFrameModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $AvatarFrameModelCopyWith<$Res> {
  factory $AvatarFrameModelCopyWith(
          AvatarFrameModel value, $Res Function(AvatarFrameModel) then) =
      _$AvatarFrameModelCopyWithImpl<$Res, AvatarFrameModel>;
  @useResult
  $Res call(
      {int? id,
      String? userId,
      int? giftId,
      String? source,
      String? giftStatus,
      DateTime? activateExpireTime,
      DateTime? createdTime,
      DateTime? expireTime,
      String? name,
      String? imageUrl,
      String? svgaUrl,
      int? price,
      String? giftFormat});
}

/// @nodoc
class _$AvatarFrameModelCopyWithImpl<$Res, $Val extends AvatarFrameModel>
    implements $AvatarFrameModelCopyWith<$Res> {
  _$AvatarFrameModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? giftId = freezed,
    Object? source = freezed,
    Object? giftStatus = freezed,
    Object? activateExpireTime = freezed,
    Object? createdTime = freezed,
    Object? expireTime = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? svgaUrl = freezed,
    Object? price = freezed,
    Object? giftFormat = freezed,
  }) {
    return _then(_value.copyWith(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      giftId: freezed == giftId
          ? _value.giftId
          : giftId // ignore: cast_nullable_to_non_nullable
              as int?,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      giftStatus: freezed == giftStatus
          ? _value.giftStatus
          : giftStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      activateExpireTime: freezed == activateExpireTime
          ? _value.activateExpireTime
          : activateExpireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdTime: freezed == createdTime
          ? _value.createdTime
          : createdTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      giftFormat: freezed == giftFormat
          ? _value.giftFormat
          : giftFormat // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$AvatarFrameModelImplCopyWith<$Res>
    implements $AvatarFrameModelCopyWith<$Res> {
  factory _$$AvatarFrameModelImplCopyWith(_$AvatarFrameModelImpl value,
          $Res Function(_$AvatarFrameModelImpl) then) =
      __$$AvatarFrameModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? id,
      String? userId,
      int? giftId,
      String? source,
      String? giftStatus,
      DateTime? activateExpireTime,
      DateTime? createdTime,
      DateTime? expireTime,
      String? name,
      String? imageUrl,
      String? svgaUrl,
      int? price,
      String? giftFormat});
}

/// @nodoc
class __$$AvatarFrameModelImplCopyWithImpl<$Res>
    extends _$AvatarFrameModelCopyWithImpl<$Res, _$AvatarFrameModelImpl>
    implements _$$AvatarFrameModelImplCopyWith<$Res> {
  __$$AvatarFrameModelImplCopyWithImpl(_$AvatarFrameModelImpl _value,
      $Res Function(_$AvatarFrameModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? id = freezed,
    Object? userId = freezed,
    Object? giftId = freezed,
    Object? source = freezed,
    Object? giftStatus = freezed,
    Object? activateExpireTime = freezed,
    Object? createdTime = freezed,
    Object? expireTime = freezed,
    Object? name = freezed,
    Object? imageUrl = freezed,
    Object? svgaUrl = freezed,
    Object? price = freezed,
    Object? giftFormat = freezed,
  }) {
    return _then(_$AvatarFrameModelImpl(
      id: freezed == id
          ? _value.id
          : id // ignore: cast_nullable_to_non_nullable
              as int?,
      userId: freezed == userId
          ? _value.userId
          : userId // ignore: cast_nullable_to_non_nullable
              as String?,
      giftId: freezed == giftId
          ? _value.giftId
          : giftId // ignore: cast_nullable_to_non_nullable
              as int?,
      source: freezed == source
          ? _value.source
          : source // ignore: cast_nullable_to_non_nullable
              as String?,
      giftStatus: freezed == giftStatus
          ? _value.giftStatus
          : giftStatus // ignore: cast_nullable_to_non_nullable
              as String?,
      activateExpireTime: freezed == activateExpireTime
          ? _value.activateExpireTime
          : activateExpireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      createdTime: freezed == createdTime
          ? _value.createdTime
          : createdTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      expireTime: freezed == expireTime
          ? _value.expireTime
          : expireTime // ignore: cast_nullable_to_non_nullable
              as DateTime?,
      name: freezed == name
          ? _value.name
          : name // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      price: freezed == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as int?,
      giftFormat: freezed == giftFormat
          ? _value.giftFormat
          : giftFormat // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$AvatarFrameModelImpl implements _AvatarFrameModel {
  const _$AvatarFrameModelImpl(
      {this.id,
      this.userId,
      this.giftId,
      this.source,
      this.giftStatus,
      this.activateExpireTime,
      this.createdTime,
      this.expireTime,
      this.name,
      this.imageUrl,
      this.svgaUrl,
      this.price,
      this.giftFormat});

  factory _$AvatarFrameModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$AvatarFrameModelImplFromJson(json);

  @override
  final int? id;
  @override
  final String? userId;
  @override
  final int? giftId;
  @override
  final String? source;
  @override
  final String? giftStatus;
  @override
  final DateTime? activateExpireTime;
  @override
  final DateTime? createdTime;
  @override
  final DateTime? expireTime;
  @override
  final String? name;
  @override
  final String? imageUrl;
  @override
  final String? svgaUrl;
  @override
  final int? price;
  @override
  final String? giftFormat;

  @override
  String toString() {
    return 'AvatarFrameModel(id: $id, userId: $userId, giftId: $giftId, source: $source, giftStatus: $giftStatus, activateExpireTime: $activateExpireTime, createdTime: $createdTime, expireTime: $expireTime, name: $name, imageUrl: $imageUrl, svgaUrl: $svgaUrl, price: $price, giftFormat: $giftFormat)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$AvatarFrameModelImpl &&
            (identical(other.id, id) || other.id == id) &&
            (identical(other.userId, userId) || other.userId == userId) &&
            (identical(other.giftId, giftId) || other.giftId == giftId) &&
            (identical(other.source, source) || other.source == source) &&
            (identical(other.giftStatus, giftStatus) ||
                other.giftStatus == giftStatus) &&
            (identical(other.activateExpireTime, activateExpireTime) ||
                other.activateExpireTime == activateExpireTime) &&
            (identical(other.createdTime, createdTime) ||
                other.createdTime == createdTime) &&
            (identical(other.expireTime, expireTime) ||
                other.expireTime == expireTime) &&
            (identical(other.name, name) || other.name == name) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.svgaUrl, svgaUrl) || other.svgaUrl == svgaUrl) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.giftFormat, giftFormat) ||
                other.giftFormat == giftFormat));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      id,
      userId,
      giftId,
      source,
      giftStatus,
      activateExpireTime,
      createdTime,
      expireTime,
      name,
      imageUrl,
      svgaUrl,
      price,
      giftFormat);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$AvatarFrameModelImplCopyWith<_$AvatarFrameModelImpl> get copyWith =>
      __$$AvatarFrameModelImplCopyWithImpl<_$AvatarFrameModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$AvatarFrameModelImplToJson(
      this,
    );
  }
}

abstract class _AvatarFrameModel implements AvatarFrameModel {
  const factory _AvatarFrameModel(
      {final int? id,
      final String? userId,
      final int? giftId,
      final String? source,
      final String? giftStatus,
      final DateTime? activateExpireTime,
      final DateTime? createdTime,
      final DateTime? expireTime,
      final String? name,
      final String? imageUrl,
      final String? svgaUrl,
      final int? price,
      final String? giftFormat}) = _$AvatarFrameModelImpl;

  factory _AvatarFrameModel.fromJson(Map<String, dynamic> json) =
      _$AvatarFrameModelImpl.fromJson;

  @override
  int? get id;
  @override
  String? get userId;
  @override
  int? get giftId;
  @override
  String? get source;
  @override
  String? get giftStatus;
  @override
  DateTime? get activateExpireTime;
  @override
  DateTime? get createdTime;
  @override
  DateTime? get expireTime;
  @override
  String? get name;
  @override
  String? get imageUrl;
  @override
  String? get svgaUrl;
  @override
  int? get price;
  @override
  String? get giftFormat;
  @override
  @JsonKey(ignore: true)
  _$$AvatarFrameModelImplCopyWith<_$AvatarFrameModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
