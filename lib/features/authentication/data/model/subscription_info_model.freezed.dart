// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'subscription_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

SubscriptionInfoModel _$SubscriptionInfoModelFromJson(
    Map<String, dynamic> json) {
  return _SubscriptionInfoModel.fromJson(json);
}

/// @nodoc
mixin _$SubscriptionInfoModel {
  bool get subscribed => throw _privateConstructorUsedError;
  String get expiresTime => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $SubscriptionInfoModelCopyWith<SubscriptionInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $SubscriptionInfoModelCopyWith<$Res> {
  factory $SubscriptionInfoModelCopyWith(SubscriptionInfoModel value,
          $Res Function(SubscriptionInfoModel) then) =
      _$SubscriptionInfoModelCopyWithImpl<$Res, SubscriptionInfoModel>;
  @useResult
  $Res call({bool subscribed, String expiresTime});
}

/// @nodoc
class _$SubscriptionInfoModelCopyWithImpl<$Res,
        $Val extends SubscriptionInfoModel>
    implements $SubscriptionInfoModelCopyWith<$Res> {
  _$SubscriptionInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subscribed = null,
    Object? expiresTime = null,
  }) {
    return _then(_value.copyWith(
      subscribed: null == subscribed
          ? _value.subscribed
          : subscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresTime: null == expiresTime
          ? _value.expiresTime
          : expiresTime // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$SubscriptionInfoModelImplCopyWith<$Res>
    implements $SubscriptionInfoModelCopyWith<$Res> {
  factory _$$SubscriptionInfoModelImplCopyWith(
          _$SubscriptionInfoModelImpl value,
          $Res Function(_$SubscriptionInfoModelImpl) then) =
      __$$SubscriptionInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({bool subscribed, String expiresTime});
}

/// @nodoc
class __$$SubscriptionInfoModelImplCopyWithImpl<$Res>
    extends _$SubscriptionInfoModelCopyWithImpl<$Res,
        _$SubscriptionInfoModelImpl>
    implements _$$SubscriptionInfoModelImplCopyWith<$Res> {
  __$$SubscriptionInfoModelImplCopyWithImpl(_$SubscriptionInfoModelImpl _value,
      $Res Function(_$SubscriptionInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? subscribed = null,
    Object? expiresTime = null,
  }) {
    return _then(_$SubscriptionInfoModelImpl(
      subscribed: null == subscribed
          ? _value.subscribed
          : subscribed // ignore: cast_nullable_to_non_nullable
              as bool,
      expiresTime: null == expiresTime
          ? _value.expiresTime
          : expiresTime // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$SubscriptionInfoModelImpl implements _SubscriptionInfoModel {
  const _$SubscriptionInfoModelImpl(
      {this.subscribed = false, this.expiresTime = ''});

  factory _$SubscriptionInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$SubscriptionInfoModelImplFromJson(json);

  @override
  @JsonKey()
  final bool subscribed;
  @override
  @JsonKey()
  final String expiresTime;

  @override
  String toString() {
    return 'SubscriptionInfoModel(subscribed: $subscribed, expiresTime: $expiresTime)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$SubscriptionInfoModelImpl &&
            (identical(other.subscribed, subscribed) ||
                other.subscribed == subscribed) &&
            (identical(other.expiresTime, expiresTime) ||
                other.expiresTime == expiresTime));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, subscribed, expiresTime);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$SubscriptionInfoModelImplCopyWith<_$SubscriptionInfoModelImpl>
      get copyWith => __$$SubscriptionInfoModelImplCopyWithImpl<
          _$SubscriptionInfoModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$SubscriptionInfoModelImplToJson(
      this,
    );
  }
}

abstract class _SubscriptionInfoModel implements SubscriptionInfoModel {
  const factory _SubscriptionInfoModel(
      {final bool subscribed,
      final String expiresTime}) = _$SubscriptionInfoModelImpl;

  factory _SubscriptionInfoModel.fromJson(Map<String, dynamic> json) =
      _$SubscriptionInfoModelImpl.fromJson;

  @override
  bool get subscribed;
  @override
  String get expiresTime;
  @override
  @JsonKey(ignore: true)
  _$$SubscriptionInfoModelImplCopyWith<_$SubscriptionInfoModelImpl>
      get copyWith => throw _privateConstructorUsedError;
}
