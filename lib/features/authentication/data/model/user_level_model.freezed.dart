// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_level_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

UserLevelModel _$UserLevelModelFromJson(Map<String, dynamic> json) {
  return _UserLevelModel.fromJson(json);
}

/// @nodoc
mixin _$UserLevelModel {
  int? get curGemSpent => throw _privateConstructorUsedError;
  int? get curLevel => throw _privateConstructorUsedError;
  int? get nextLevel => throw _privateConstructorUsedError;
  int? get curLevelRequiredGems => throw _privateConstructorUsedError;
  int? get nextLevelRequiredGems => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $UserLevelModelCopyWith<UserLevelModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $UserLevelModelCopyWith<$Res> {
  factory $UserLevelModelCopyWith(
          UserLevelModel value, $Res Function(UserLevelModel) then) =
      _$UserLevelModelCopyWithImpl<$Res, UserLevelModel>;
  @useResult
  $Res call(
      {int? curGemSpent,
      int? curLevel,
      int? nextLevel,
      int? curLevelRequiredGems,
      int? nextLevelRequiredGems});
}

/// @nodoc
class _$UserLevelModelCopyWithImpl<$Res, $Val extends UserLevelModel>
    implements $UserLevelModelCopyWith<$Res> {
  _$UserLevelModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? curGemSpent = freezed,
    Object? curLevel = freezed,
    Object? nextLevel = freezed,
    Object? curLevelRequiredGems = freezed,
    Object? nextLevelRequiredGems = freezed,
  }) {
    return _then(_value.copyWith(
      curGemSpent: freezed == curGemSpent
          ? _value.curGemSpent
          : curGemSpent // ignore: cast_nullable_to_non_nullable
              as int?,
      curLevel: freezed == curLevel
          ? _value.curLevel
          : curLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      nextLevel: freezed == nextLevel
          ? _value.nextLevel
          : nextLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      curLevelRequiredGems: freezed == curLevelRequiredGems
          ? _value.curLevelRequiredGems
          : curLevelRequiredGems // ignore: cast_nullable_to_non_nullable
              as int?,
      nextLevelRequiredGems: freezed == nextLevelRequiredGems
          ? _value.nextLevelRequiredGems
          : nextLevelRequiredGems // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$UserLevelModelImplCopyWith<$Res>
    implements $UserLevelModelCopyWith<$Res> {
  factory _$$UserLevelModelImplCopyWith(_$UserLevelModelImpl value,
          $Res Function(_$UserLevelModelImpl) then) =
      __$$UserLevelModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {int? curGemSpent,
      int? curLevel,
      int? nextLevel,
      int? curLevelRequiredGems,
      int? nextLevelRequiredGems});
}

/// @nodoc
class __$$UserLevelModelImplCopyWithImpl<$Res>
    extends _$UserLevelModelCopyWithImpl<$Res, _$UserLevelModelImpl>
    implements _$$UserLevelModelImplCopyWith<$Res> {
  __$$UserLevelModelImplCopyWithImpl(
      _$UserLevelModelImpl _value, $Res Function(_$UserLevelModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? curGemSpent = freezed,
    Object? curLevel = freezed,
    Object? nextLevel = freezed,
    Object? curLevelRequiredGems = freezed,
    Object? nextLevelRequiredGems = freezed,
  }) {
    return _then(_$UserLevelModelImpl(
      curGemSpent: freezed == curGemSpent
          ? _value.curGemSpent
          : curGemSpent // ignore: cast_nullable_to_non_nullable
              as int?,
      curLevel: freezed == curLevel
          ? _value.curLevel
          : curLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      nextLevel: freezed == nextLevel
          ? _value.nextLevel
          : nextLevel // ignore: cast_nullable_to_non_nullable
              as int?,
      curLevelRequiredGems: freezed == curLevelRequiredGems
          ? _value.curLevelRequiredGems
          : curLevelRequiredGems // ignore: cast_nullable_to_non_nullable
              as int?,
      nextLevelRequiredGems: freezed == nextLevelRequiredGems
          ? _value.nextLevelRequiredGems
          : nextLevelRequiredGems // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$UserLevelModelImpl implements _UserLevelModel {
  const _$UserLevelModelImpl(
      {this.curGemSpent,
      this.curLevel,
      this.nextLevel,
      this.curLevelRequiredGems,
      this.nextLevelRequiredGems});

  factory _$UserLevelModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$UserLevelModelImplFromJson(json);

  @override
  final int? curGemSpent;
  @override
  final int? curLevel;
  @override
  final int? nextLevel;
  @override
  final int? curLevelRequiredGems;
  @override
  final int? nextLevelRequiredGems;

  @override
  String toString() {
    return 'UserLevelModel(curGemSpent: $curGemSpent, curLevel: $curLevel, nextLevel: $nextLevel, curLevelRequiredGems: $curLevelRequiredGems, nextLevelRequiredGems: $nextLevelRequiredGems)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$UserLevelModelImpl &&
            (identical(other.curGemSpent, curGemSpent) ||
                other.curGemSpent == curGemSpent) &&
            (identical(other.curLevel, curLevel) ||
                other.curLevel == curLevel) &&
            (identical(other.nextLevel, nextLevel) ||
                other.nextLevel == nextLevel) &&
            (identical(other.curLevelRequiredGems, curLevelRequiredGems) ||
                other.curLevelRequiredGems == curLevelRequiredGems) &&
            (identical(other.nextLevelRequiredGems, nextLevelRequiredGems) ||
                other.nextLevelRequiredGems == nextLevelRequiredGems));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, curGemSpent, curLevel, nextLevel,
      curLevelRequiredGems, nextLevelRequiredGems);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$UserLevelModelImplCopyWith<_$UserLevelModelImpl> get copyWith =>
      __$$UserLevelModelImplCopyWithImpl<_$UserLevelModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$UserLevelModelImplToJson(
      this,
    );
  }
}

abstract class _UserLevelModel implements UserLevelModel {
  const factory _UserLevelModel(
      {final int? curGemSpent,
      final int? curLevel,
      final int? nextLevel,
      final int? curLevelRequiredGems,
      final int? nextLevelRequiredGems}) = _$UserLevelModelImpl;

  factory _UserLevelModel.fromJson(Map<String, dynamic> json) =
      _$UserLevelModelImpl.fromJson;

  @override
  int? get curGemSpent;
  @override
  int? get curLevel;
  @override
  int? get nextLevel;
  @override
  int? get curLevelRequiredGems;
  @override
  int? get nextLevelRequiredGems;
  @override
  @JsonKey(ignore: true)
  _$$UserLevelModelImplCopyWith<_$UserLevelModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
