import 'package:freezed_annotation/freezed_annotation.dart';

part 'profile_model.freezed.dart';
part 'profile_model.g.dart';

enum UserGender {
  @JsonValue(1)
  male(1),
  @JsonValue(2)
  female(2),
  @JsonValue(0)
  other(0);

  final int value;
  const UserGender(this.value);

  // Convert enum to int for JSON
  int toJson() => value;

  // Create enum from int
  static UserGender fromJson(int json) {
    return UserGender.values.firstWhere(
      (gender) => gender.value == json,
      orElse: () => UserGender.other,
    );
  }
}

enum DeleteStatus {
  @JsonValue(0)
  none(0),
  @JsonValue(1)
  pending(1),
  @JsonValue(2)
  deleted(2),
  @JsonValue(3)
  hidden(3),
  ;

  final int value;
  const DeleteStatus(this.value);

  int toJson() => value;

  // Create enum from int
  static DeleteStatus fromJson(int json) {
    return DeleteStatus.values.firstWhere(
      (status) => status.value == json,
      orElse: () => DeleteStatus.none,
    );
  }
}

@freezed
class ProfileModel with _$ProfileModel {
  @JsonSerializable(explicitToJson: true)
  const factory ProfileModel({
    final String? id,
    final String? nickName,
    final String? role,
    final String? avatar,
    final UserGender? gender,
    final String? profileMessage,
    final DateTime? birthday,
    final bool? banned,
    final DeleteStatus? deleteStatus,
  }) = _ProfileModel;

  factory ProfileModel.fromJson(Map<String, dynamic> json) =>
      _$ProfileModelFromJson(json);
}
