// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'extra_user_info_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

ExtraUserInfoModel _$ExtraUserInfoModelFromJson(Map<String, dynamic> json) {
  return _ExtraUserInfoModel.fromJson(json);
}

/// @nodoc
mixin _$ExtraUserInfoModel {
  @JsonKey(name: 'userLevelVO')
  UserLevelModel? get userLevel => throw _privateConstructorUsedError;
  @JsonKey(name: 'avatarInfoVO')
  AvatarFrameModel? get avatarInfo => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ExtraUserInfoModelCopyWith<ExtraUserInfoModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ExtraUserInfoModelCopyWith<$Res> {
  factory $ExtraUserInfoModelCopyWith(
          ExtraUserInfoModel value, $Res Function(ExtraUserInfoModel) then) =
      _$ExtraUserInfoModelCopyWithImpl<$Res, ExtraUserInfoModel>;
  @useResult
  $Res call(
      {@JsonKey(name: 'userLevelVO') UserLevelModel? userLevel,
      @JsonKey(name: 'avatarInfoVO') AvatarFrameModel? avatarInfo});

  $UserLevelModelCopyWith<$Res>? get userLevel;
  $AvatarFrameModelCopyWith<$Res>? get avatarInfo;
}

/// @nodoc
class _$ExtraUserInfoModelCopyWithImpl<$Res, $Val extends ExtraUserInfoModel>
    implements $ExtraUserInfoModelCopyWith<$Res> {
  _$ExtraUserInfoModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userLevel = freezed,
    Object? avatarInfo = freezed,
  }) {
    return _then(_value.copyWith(
      userLevel: freezed == userLevel
          ? _value.userLevel
          : userLevel // ignore: cast_nullable_to_non_nullable
              as UserLevelModel?,
      avatarInfo: freezed == avatarInfo
          ? _value.avatarInfo
          : avatarInfo // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
    ) as $Val);
  }

  @override
  @pragma('vm:prefer-inline')
  $UserLevelModelCopyWith<$Res>? get userLevel {
    if (_value.userLevel == null) {
      return null;
    }

    return $UserLevelModelCopyWith<$Res>(_value.userLevel!, (value) {
      return _then(_value.copyWith(userLevel: value) as $Val);
    });
  }

  @override
  @pragma('vm:prefer-inline')
  $AvatarFrameModelCopyWith<$Res>? get avatarInfo {
    if (_value.avatarInfo == null) {
      return null;
    }

    return $AvatarFrameModelCopyWith<$Res>(_value.avatarInfo!, (value) {
      return _then(_value.copyWith(avatarInfo: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$ExtraUserInfoModelImplCopyWith<$Res>
    implements $ExtraUserInfoModelCopyWith<$Res> {
  factory _$$ExtraUserInfoModelImplCopyWith(_$ExtraUserInfoModelImpl value,
          $Res Function(_$ExtraUserInfoModelImpl) then) =
      __$$ExtraUserInfoModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'userLevelVO') UserLevelModel? userLevel,
      @JsonKey(name: 'avatarInfoVO') AvatarFrameModel? avatarInfo});

  @override
  $UserLevelModelCopyWith<$Res>? get userLevel;
  @override
  $AvatarFrameModelCopyWith<$Res>? get avatarInfo;
}

/// @nodoc
class __$$ExtraUserInfoModelImplCopyWithImpl<$Res>
    extends _$ExtraUserInfoModelCopyWithImpl<$Res, _$ExtraUserInfoModelImpl>
    implements _$$ExtraUserInfoModelImplCopyWith<$Res> {
  __$$ExtraUserInfoModelImplCopyWithImpl(_$ExtraUserInfoModelImpl _value,
      $Res Function(_$ExtraUserInfoModelImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? userLevel = freezed,
    Object? avatarInfo = freezed,
  }) {
    return _then(_$ExtraUserInfoModelImpl(
      userLevel: freezed == userLevel
          ? _value.userLevel
          : userLevel // ignore: cast_nullable_to_non_nullable
              as UserLevelModel?,
      avatarInfo: freezed == avatarInfo
          ? _value.avatarInfo
          : avatarInfo // ignore: cast_nullable_to_non_nullable
              as AvatarFrameModel?,
    ));
  }
}

/// @nodoc

@JsonSerializable(explicitToJson: true)
class _$ExtraUserInfoModelImpl implements _ExtraUserInfoModel {
  const _$ExtraUserInfoModelImpl(
      {@JsonKey(name: 'userLevelVO') this.userLevel,
      @JsonKey(name: 'avatarInfoVO') this.avatarInfo});

  factory _$ExtraUserInfoModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$ExtraUserInfoModelImplFromJson(json);

  @override
  @JsonKey(name: 'userLevelVO')
  final UserLevelModel? userLevel;
  @override
  @JsonKey(name: 'avatarInfoVO')
  final AvatarFrameModel? avatarInfo;

  @override
  String toString() {
    return 'ExtraUserInfoModel(userLevel: $userLevel, avatarInfo: $avatarInfo)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ExtraUserInfoModelImpl &&
            (identical(other.userLevel, userLevel) ||
                other.userLevel == userLevel) &&
            (identical(other.avatarInfo, avatarInfo) ||
                other.avatarInfo == avatarInfo));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, userLevel, avatarInfo);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ExtraUserInfoModelImplCopyWith<_$ExtraUserInfoModelImpl> get copyWith =>
      __$$ExtraUserInfoModelImplCopyWithImpl<_$ExtraUserInfoModelImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ExtraUserInfoModelImplToJson(
      this,
    );
  }
}

abstract class _ExtraUserInfoModel implements ExtraUserInfoModel {
  const factory _ExtraUserInfoModel(
          {@JsonKey(name: 'userLevelVO') final UserLevelModel? userLevel,
          @JsonKey(name: 'avatarInfoVO') final AvatarFrameModel? avatarInfo}) =
      _$ExtraUserInfoModelImpl;

  factory _ExtraUserInfoModel.fromJson(Map<String, dynamic> json) =
      _$ExtraUserInfoModelImpl.fromJson;

  @override
  @JsonKey(name: 'userLevelVO')
  UserLevelModel? get userLevel;
  @override
  @JsonKey(name: 'avatarInfoVO')
  AvatarFrameModel? get avatarInfo;
  @override
  @JsonKey(ignore: true)
  _$$ExtraUserInfoModelImplCopyWith<_$ExtraUserInfoModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
