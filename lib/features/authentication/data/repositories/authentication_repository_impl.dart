import 'package:flutter_audio_room/features/authentication/data/datasource/auth_remote_data_source.dart';
import 'package:flutter_audio_room/features/authentication/data/model/contact_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/signup_profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_level_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/wallet_model.dart';
import 'package:flutter_audio_room/features/authentication/domain/repositories/auth_repository.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

class AuthenticationRepositoryImpl extends AuthenticationRepository {
  final LoginUserDataSource dataSource;

  AuthenticationRepositoryImpl(this.dataSource);

  @override
  Future<ResultWithData<String?>> sendCode({
    required String sendTo,
    String? countryCode,
    required AuthType type,
  }) async {
    return dataSource.sendCode(
        sendTo: sendTo, countryCode: countryCode, type: type);
  }

  @override
  Future<VoidResult> checkUsername({required String username}) {
    return dataSource.checkUsername(username: username);
  }

  @override
  Future<ResultWithData<Map<String, dynamic>?>> refreshToken(
      {required String refreshToken}) {
    return dataSource.refreshToken(refreshToken: refreshToken);
  }

  @override
  Future<ResultWithData<UserInfoModel>> login(
      {required SignupProfileModel profile, required AuthType type}) async {
    return dataSource.login(profile: profile, type: type);
  }

  @override
  Future<VoidResult> logout() async {
    return dataSource.logout();
  }

  @override
  Future<ResultWithData<ContactInfoModel>> getContactInfo() {
    return dataSource.getContactInfo();
  }

  @override
  Future<VoidResult> sendVerificationCode2Self({required bool isPhone}) {
    return dataSource.sendVerificationCode2Self(isPhone: isPhone);
  }

  @override
  Future<VoidResult> deleteAccount({
    required bool useEmail,
    required String code,
  }) async {
    return dataSource.deleteAccount(
      useEmail: useEmail,
      code: code,
    );
  }

  @override
  Future<VoidResult> hideAccount({
    required bool useEmail,
    required String code,
  }) async {
    return dataSource.hideAccount(useEmail: useEmail, code: code);
  }

  @override
  Future<VoidResult> unHideAccount() async {
    return dataSource.unHideAccount();
  }

  @override
  Future<VoidResult> restoreAccount() async {
    return dataSource.restoreAccount();
  }

  @override
  Future<VoidResult> resetPassword({
    required bool isPhone,
    required String code,
    required String password,
  }) async {
    return dataSource.resetPassword(
      isPhone: isPhone,
      code: code,
      password: password,
    );
  }



  @override
  Future<ResultWithData<UserInfoModel>> signup(
      {required SignupProfileModel profile, required AuthType type}) async {
    return dataSource.signup(profile: profile, type: type);
  }

  @override
  Future<VoidResult> updateIpAddress({required String ipAddress}) async {
    return dataSource.updateIpAddress(ipAddress: ipAddress);
  }

  @override
  Future<VoidResult> updateLocation({
    required String countryCode,
    required String cityName,
    required double latitude,
    required double longitude,
  }) async {
    return dataSource.updateLocation(
      countryCode: countryCode,
      cityName: cityName,
      latitude: latitude,
      longitude: longitude,
    );
  }

  @override
  Future<ResultWithData<UserLevelModel>> getUserLevel() {
    return dataSource.getUserLevel();
  }

  @override
  Future<ResultWithData<BagGiftModel>> getCurrentFrame() {
    return dataSource.getCurrentFrame();
  }

  @override
  Future<ResultWithData<WalletModel>> getUserWallet() {
    return dataSource.getUserWallet();
  }
}
