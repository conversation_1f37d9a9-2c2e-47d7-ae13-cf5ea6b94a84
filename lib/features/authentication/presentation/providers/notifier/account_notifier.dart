import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/analytics_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/other_user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/datasource/auth_remote_data_source.dart';
import 'package:flutter_audio_room/features/authentication/data/model/avatar_frame_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/contact_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/signup_profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/domain/repositories/auth_repository.dart';
import 'package:flutter_audio_room/features/authentication/domain/repositories/profile_repository.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/state/account_state.dart';
import 'package:flutter_audio_room/features/my/presentation/provider/follow_provider.dart';
import 'package:flutter_audio_room/services/token_refresh/model/session_info_model.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:flutter_audio_room/services/user_cache_service/domain/repositories/user_cache_repository.dart';
import 'package:flutter_audio_room/shared/data/consts/sp_keys.dart';
import 'package:flutter_audio_room/shared/data/local/storage_service.dart';
import 'package:flutter_audio_room/shared/domain/models/either.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';
import 'package:flutter_audio_room/shared/exceptions/app_exception.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class AccountNotifier extends StateNotifier<AccountState> {
  final AuthenticationRepository authRepository;
  final UserCacheRepository userRepository;
  final StorageService storageService;
  final TokenRefreshService tokenRefreshService;
  final Ref ref;
  late final IProfileRepository _profileRepository;

  AccountNotifier({
    required this.authRepository,
    required this.userRepository,
    required this.tokenRefreshService,
    required this.storageService,
    required this.ref,
  }) : super(const AccountState()) {
    _profileRepository = getIt<IProfileRepository>();
  }

  // Initialize user state
  Future<void> initializeUser() async {
    final user = userRepository.fetchUser();
    user.fold(
      (left) => state = state.copyWith(error: left),
      (right) async {
        if (right.profile?.deleteStatus != DeleteStatus.none) {
          logout();
          return;
        }
        
        final sessionInfo = await getSessionInfo();
        // TODO: remove test
        // await _saveSessionInfo(sessionInfo.copyWith(accessToken: 'test'));
        state = state.copyWith(
          userInfo: right.copyWith(sessionInfo: sessionInfo),
          status: AccountStatus.authenticated,
        );
        LogUtils.d(right.toString(), tag: 'AuthNotifier.initializeUser');
      },
    );
  }

  Future<ResultWithData<String?>> sendCode({
    required String sendTo,
    required String? countryCode,
    required AuthType type,
  }) async {
    return authRepository.sendCode(
      sendTo: sendTo,
      countryCode: countryCode,
      type: type,
    );
  }

  Future<void> signup({
    required SignupProfileModel profile,
    required AuthType type,
  }) async {
    state = state.copyWith(error: null);
    final result = await authRepository.signup(profile: profile, type: type);
    state = await result.fold(
      (failure) async {
        // Track signup failure
        AnalyticsUtils.trackSignup(
          authType: type,
          method: profile.password != null ? 'password' : 'verification_code',
          success: false,
          errorMessage: failure.message,
        );
        return state.copyWith(error: failure);
      },
      (user) async {
        try {
          await _saveSessionInfo(user.sessionInfo);

          final hasSavedUser = await userRepository.saveUser(user: user);
          if (hasSavedUser) {
            // Track successful signup
            AnalyticsUtils.trackSignup(
              authType: type,
              method:
                  profile.password != null ? 'password' : 'verification_code',
              success: true,
            );

            // Set user properties
            AnalyticsUtils.setUserProperties(
              userId: user.profile?.id?.toString() ?? '',
              username: user.profile?.nickName,
              // Note: ProfileModel doesn't have countryCode, we can get it from SignupProfile if needed
            );

            return state.copyWith(
              status: AccountStatus.authenticated,
              userInfo: user,
            );
          }
          return state.copyWith(error: CacheFailureException());
        } catch (e) {
          // Track signup cache error
          AnalyticsUtils.trackError(
            errorType: 'signup_cache_error',
            errorMessage: e.toString(),
            context: 'AuthNotifier.signup',
          );

          return state.copyWith(
            error: AppException(
              identifier: 'AuthNotifier.signup',
              statusCode: 400,
              message: e.toString(),
            ),
          );
        }
      },
    );
  }

  // Direct signup method that returns result for immediate handling
  Future<Either<AppException, UserInfoModel>> signupDirect({
    required SignupProfileModel profile,
    required AuthType type,
  }) async {
    final result = await authRepository.signup(profile: profile, type: type);
    return await result.fold(
      (failure) async => Left(failure),
      (user) async {
        try {
          await _saveSessionInfo(user.sessionInfo);

          final hasSavedUser = await userRepository.saveUser(user: user);
          if (hasSavedUser) {
            state = state.copyWith(
              status: AccountStatus.authenticated,
              userInfo: user,
            );
            return Right(user);
          }
          return Left(CacheFailureException());
        } catch (e) {
          return Left(AppException(
            identifier: 'AuthNotifier.signupDirect',
            statusCode: 400,
            message: e.toString(),
          ));
        }
      },
    );
  }

  Future<void> login({
    required SignupProfileModel profile,
    required AuthType type,
  }) async {
    state = state.copyWith(error: null);
    final result = await authRepository.login(
      profile: profile,
      type: type,
    );
    state = await result.fold(
      (failure) async {
        // Track login failure
        AnalyticsUtils.trackLogin(
          authType: type,
          method: profile.password != null ? 'password' : 'verification_code',
          success: false,
          errorMessage: failure.message,
        );
        return state.copyWith(error: failure);
      },
      (user) async {
        try {
          await _saveSessionInfo(user.sessionInfo);

          final hasSavedUser = await userRepository.saveUser(user: user);
          if (hasSavedUser) {
            // Track successful login
            AnalyticsUtils.trackLogin(
              authType: type,
              method:
                  profile.password != null ? 'password' : 'verification_code',
              success: true,
            );

            // Set user properties
            AnalyticsUtils.setUserProperties(
              userId: user.profile?.id?.toString() ?? '',
              username: user.profile?.nickName,
            );

            return state.copyWith(
              status: AccountStatus.authenticated,
              userInfo: user,
            );
          }
          return state.copyWith(error: CacheFailureException());
        } catch (e) {
          // Track login cache error
          AnalyticsUtils.trackError(
            errorType: 'login_cache_error',
            errorMessage: e.toString(),
            context: 'AuthNotifier.login',
          );

          return state.copyWith(
            error: AppException(
              identifier: 'AuthNotifier.login',
              statusCode: 400,
              message: e.toString(),
            ),
          );
        }
      },
    );
  }

  // Direct login method that returns result for immediate handling
  Future<Either<AppException, UserInfoModel>> loginDirect({
    required SignupProfileModel profile,
    required AuthType type,
  }) async {
    final result = await authRepository.login(
      profile: profile,
      type: type,
    );
    return await result.fold(
      (failure) async => Left(failure),
      (user) async {
        try {
          await _saveSessionInfo(user.sessionInfo);

          final hasSavedUser = await userRepository.saveUser(user: user);
          if (hasSavedUser) {
            state = state.copyWith(
              status: AccountStatus.authenticated,
              userInfo: user,
            );
            return Right(user);
          }
          return Left(CacheFailureException());
        } catch (e) {
          return Left(AppException(
            identifier: 'AuthNotifier.loginDirect',
            statusCode: 400,
            message: e.toString(),
          ));
        }
      },
    );
  }

  Future<VoidResult> checkUsername(
      {required String username}) async {
    return authRepository.checkUsername(username: username);
  }

  Future<void> updateUserFrame({
    required AvatarFrameModel avatarFrame,
  }) async {
    var user = state.userInfo;
    if (user == null) return;

    user = user.copyWith(frame: avatarFrame);

    state = state.copyWith(userInfo: user);

    final hasSavedUser = await userRepository.saveUser(user: user);
    if (!hasSavedUser) {
      state = state.copyWith(error: CacheFailureException());
    }
  }

  Future<void> getCurrentAvatarFrame() async {
    final userResult = userRepository.fetchUser();
    if (userResult.isLeft()) {
      return;
    }
    final user = userResult.getRight()!;
    final result = await authRepository.getCurrentFrame();
    if (result.isRight()) {
      final currentFrame = result.getRight()!;
      state = state.copyWith(
        userInfo: user.copyWith(
          frame: currentFrame.avatarFrame(),
        ),
      );

      final hasSavedUser = await userRepository.saveUser(user: state.userInfo!);
      if (!hasSavedUser) {
        state = state.copyWith(error: CacheFailureException());
      }
    }
  }

  Future<void> getUserWallet() async {
    final result = await authRepository.getUserWallet();
    if (result.isRight()) {
      final wallet = result.getRight()!;
      final oldWallet = state.wallet;
      state = state.copyWith(wallet: wallet);

      // Track wallet balance changes (could indicate successful purchases)
      if (oldWallet != null) {
        final gemsChanged = wallet.gems - oldWallet.gems;
        final dustsChanged = wallet.dusts - oldWallet.dusts;

        if (gemsChanged != 0 || dustsChanged != 0) {
          AnalyticsUtils.trackCustomEvent(
            eventName: 'wallet_balance_changed',
            parameters: {
              'gems_change': gemsChanged,
              'dusts_change': dustsChanged,
              'new_gems_balance': wallet.gems,
              'new_dusts_balance': wallet.dusts,
            },
          );
        }
      }
    }
  }

  Future<void> logout() async {
    try {
      final result = await userRepository.deleteUser();
      if (result) {
        // Track successful logout
        AnalyticsUtils.trackLogout(reason: 'user_initiated');

        // Clear user data from analytics
        AnalyticsUtils.clearUserData();

        storageService.remove(SPKeys.refreshToken);
        storageService.remove(SPKeys.accessToken);
        ref.invalidateSelf();
      } else {
        // Track logout cache error
        AnalyticsUtils.trackError(
          errorType: 'logout_cache_error',
          errorMessage: 'Failed to delete user from cache',
          context: 'AuthNotifier.logout',
        );

        state = state.copyWith(
          error: CacheFailureException(),
        );
      }
    } catch (e) {
      // Track logout error
      AnalyticsUtils.trackError(
        errorType: 'logout_error',
        errorMessage: e.toString(),
        context: 'AuthNotifier.logout',
      );

      state = state.copyWith(
        error: AppException(
          identifier: 'AuthNotifier.logout',
          statusCode: 400,
          message: e.toString(),
        ),
      );
    }
  }

  Future<VoidResult> sendVerificationCode2Self({required bool isPhone}) async {
    return authRepository.sendVerificationCode2Self(isPhone: isPhone);
  }

  Future<ResultWithData<ContactInfoModel>> getContactInfo() async {
    return authRepository.getContactInfo();
  }

  Future<VoidResult> restoreAccount() async {
    return authRepository.restoreAccount().then((value) {
      return value.fold(
        (failure) {
          return Left(failure);
        },
        (right) {
          state = state.copyWith(
            userInfo: state.userInfo?.copyWith(
              profile: state.userInfo?.profile?.copyWith(
                deleteStatus: DeleteStatus.none,
              ),
            ),
          );
          return Right(right);
        },
      );
    });
  }
  /// 删除账号
  ///
  /// [useEmail] - 是否使用邮箱验证码
  /// [code] - 验证码
  Future<VoidResult> deleteAccount({
    required bool useEmail,
    required String code,
  }) async {
    try {
      // 调用删除账号 API
      final result = await authRepository.deleteAccount(
        useEmail: useEmail,
        code: code,
      );

      // 处理结果
      return result.fold(
        (failure) {
          return Left(failure);
        },
        (right) async {
          // 删除本地用户数据
          final deleteResult = await userRepository.deleteUser();
          if (deleteResult) {
            // 移除 token
            storageService.remove(SPKeys.refreshToken);
            storageService.remove(SPKeys.accessToken);
            LogUtils.d('Account deleted successfully',
                tag: 'AccountNotifier.deleteAccount');
          }

          return Right(right);
        },
      );
    } catch (e) {
      LogUtils.e(
        'Failed to delete account: $e',
        tag: 'AccountNotifier.deleteAccount',
      );
      return Left(AppException(
        identifier: 'AccountNotifier.deleteAccount',
        statusCode: 400,
        message: e.toString(),
      ));
    }
  }

  Future<VoidResult> unHideAccount() async {
    final result = await authRepository.unHideAccount();
    return result.fold(
      (failure) {
        return Left(failure);
      },
      (right) {
        state = state.copyWith(
          userInfo: state.userInfo?.copyWith(
            profile: state.userInfo?.profile?.copyWith(
              deleteStatus: DeleteStatus.none,
            ),
          ),
        );
        return Right(right);
      },
    );
  }

  Future<VoidResult> hideAccount({
    required bool useEmail,
    required String code,
  }) async {
    try {
      final result = await authRepository.hideAccount(
        useEmail: useEmail,
        code: code,
      );
      return result.fold(
        (failure) {
          return Left(failure);
        },
        (right) async {
          // 删除本地用户数据
          final deleteResult = await userRepository.deleteUser();
          if (deleteResult) {
            // 移除 token
            storageService.remove(SPKeys.refreshToken);
            storageService.remove(SPKeys.accessToken);
            LogUtils.d('Account deleted successfully',
                tag: 'AccountNotifier.deleteAccount');
          }

          return Right(right);
        },
      );
    } catch (e) {
      LogUtils.e(
        'Failed to hide account: $e',
        tag: 'AccountNotifier.hideAccount',
      );
      return Left(AppException(
        identifier: 'AccountNotifier.hideAccount',
        statusCode: 400,
        message: e.toString(),
      ));
    }
  }

  /// 重置密码
  ///
  /// [isPhone] - 是否使用手机号验证
  /// [code] - 验证码
  /// [password] - 新密码
  Future<VoidResult> resetPassword({
    required bool isPhone,
    required String code,
    required String password,
  }) async {
    try {
      final result = await authRepository.resetPassword(
        isPhone: isPhone,
        code: code,
        password: password,
      );

      return result.fold(
        (failure) {
          LogUtils.e(
            'Failed to reset password: ${failure.message}',
            tag: 'AccountNotifier.resetPassword',
          );
          return Left(failure);
        },
        (right) {
          LogUtils.d('Password reset successfully',
              tag: 'AccountNotifier.resetPassword');
          return Right(right);
        },
      );
    } catch (e) {
      LogUtils.e(
        'Failed to reset password: $e',
        tag: 'AccountNotifier.resetPassword',
      );
      return Left(AppException(
        identifier: 'AccountNotifier.resetPassword',
        statusCode: 400,
        message: e.toString(),
      ));
    }
  }



  Future<VoidResult> updateLocation({
    required String countryCode,
    required String cityName,
    required double latitude,
    required double longitude,
  }) async {
    return authRepository.updateLocation(
      countryCode: countryCode,
      cityName: cityName,
      latitude: latitude,
      longitude: longitude,
    );
  }

  Future<VoidResult> updateIpAddress({required String ipAddress}) async {
    return authRepository.updateIpAddress(ipAddress: ipAddress);
  }

  Future<SessionInfoModel> getSessionInfo() async {
    final accessToken = storageService.accessToken;
    final refreshToken = storageService.refreshToken;
    return SessionInfoModel(
        accessToken: accessToken, refreshToken: refreshToken);
  }

  Future<void> _saveSessionInfo(SessionInfoModel? sessionInfo) async {
    if (sessionInfo == null) return;
    await storageService.setAccessToken(sessionInfo.accessToken);
    await storageService.setRefreshToken(sessionInfo.refreshToken);
  }

  /// Fetch subscription info and update user state
  Future<void> getSubscriptionInfo() async {
    try {
      final result = await _profileRepository.getSubscriptionInfo();
      result.fold(
        (error) {
          LogUtils.e('Failed to fetch subscription info: ${error.message}',
              tag: 'AccountNotifier.getSubscriptionInfo');
        },
        (subscriptionInfo) {
          final currentUser = state.userInfo;
          if (currentUser != null) {
            final updatedUser = currentUser.copyWith(
              subscriptionInfo: subscriptionInfo,
            );
            state = state.copyWith(userInfo: updatedUser);

            // Save updated user to cache
            userRepository.saveUser(user: updatedUser);
          }
        },
      );
    } catch (e) {
      LogUtils.e('Error fetching subscription info: $e',
          tag: 'AccountNotifier.getSubscriptionInfo');
    }
  }

  /// Fetch personal info and update user state and follow counts
  Future<void> getPersonalInfo() async {
    try {
      final result = await _profileRepository.getPersonalInfo();
      result.fold(
        (error) {
          LogUtils.e('Failed to fetch personal info: ${error.message}',
              tag: 'AccountNotifier.getPersonalInfo');
        },
        (personalInfo) {
          final currentUser = state.userInfo;
          if (currentUser != null) {
            // Update user info with profile data
            final updatedUser = currentUser.copyWith(
              profile: personalInfo.profileVO,
              subscriptionInfo: personalInfo.userSubscriptionInfo,
            );
            state = state.copyWith(userInfo: updatedUser);

            // Save updated user to cache
            userRepository.saveUser(user: updatedUser);

            // Update follow counts in follow provider
            if (personalInfo.profileVO?.id != null) {
              ref.read(followProvider.notifier).setFollowCount(
                    OtherUserInfoModel(
                      profile: personalInfo.profileVO,
                      followeeCount: personalInfo.followeeCount,
                      followerCount: personalInfo.followerCount,
                      mutualFollowCount: personalInfo.mutualFollowCount,
                    ),
                  );
            }
          }
        },
      );
    } catch (e) {
      LogUtils.e('Error fetching personal info: $e',
          tag: 'AccountNotifier.getPersonalInfo');
    }
  }

  /// Refresh all user data (subscription info and personal info)
  Future<void> refreshUserData() async {
    await Future.wait([
      getPersonalInfo(),
      getCurrentAvatarFrame(),
      getUserWallet(),
      getSubscriptionInfo(),
    ]);
  }
}
