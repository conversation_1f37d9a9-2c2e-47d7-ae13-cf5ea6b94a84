import 'package:flutter_audio_room/features/authentication/data/datasource/auth_remote_data_source.dart';
import 'package:flutter_audio_room/features/authentication/data/model/contact_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/signup_profile_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_info_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/user_level_model.dart';
import 'package:flutter_audio_room/features/authentication/data/model/wallet_model.dart';
import 'package:flutter_audio_room/services/gift_service/data/model/bag_gift_model.dart';
import 'package:flutter_audio_room/shared/domain/types/common_types.dart';

abstract class AuthenticationRepository {
  Future<ResultWithData<String?>> sendCode({
    required String sendTo,
    String? countryCode,
    required AuthType type,
  });
  Future<VoidResult> checkUsername({required String username});
  Future<ResultWithData<Map<String, dynamic>?>> refreshToken(
      {required String refreshToken});
  Future<ResultWithData<UserInfoModel>> signup(
      {required SignupProfileModel profile, required AuthType type});
  Future<ResultWithData<UserInfoModel>> login({
    required SignupProfileModel profile,
    required AuthType type,
  });
  Future<VoidResult> logout();
  Future<VoidResult> deleteAccount({
    required bool useEmail,
    required String code,
  });
  Future<VoidResult> hideAccount({
    required bool useEmail,
    required String code,
  });
  Future<VoidResult> unHideAccount();
  Future<VoidResult> restoreAccount();
  Future<ResultWithData<ContactInfoModel>> getContactInfo();
  Future<VoidResult> sendVerificationCode2Self({required bool isPhone});

  Future<VoidResult> resetPassword({
    required bool isPhone,
    required String code,
    required String password,
  });

  Future<VoidResult> updateIpAddress({required String ipAddress});

  Future<VoidResult> updateLocation({
    required String countryCode,
    required String cityName,
    required double latitude,
    required double longitude,
  });

  Future<ResultWithData<UserLevelModel>> getUserLevel();

  Future<ResultWithData<BagGiftModel>> getCurrentFrame();

  Future<ResultWithData<WalletModel>> getUserWallet();
}
