import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_list_item.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/join_room_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_card.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class RoomSearchScreen extends ConsumerStatefulWidget {
  const RoomSearchScreen({super.key});

  @override
  ConsumerState<RoomSearchScreen> createState() => _RoomSearchScreenState();
}

class _RoomSearchScreenState extends ConsumerState<RoomSearchScreen>
    with JoinRoomMixin {
  final TextEditingController _searchController = TextEditingController();

  List<RoomListItem> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;
  int _currentPage = 1;
  String _lastSearchKeyword = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    _error = null;
    await _fetchRooms();
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;
    _currentPage++;
    await _fetchRooms();
  }

  Future<void> _fetchRooms() async {
    if (_isLoading) return;

    final currentKeyword = _searchController.text;
    if (currentKeyword.isEmpty) {
      setState(() {
        _dataList = [];
        _hasMore = false;
        _error = null;
      });
      return;
    }

    // 如果搜索关键词变化，重置状态
    if (currentKeyword != _lastSearchKeyword) {
      _currentPage = 1;
      _lastSearchKeyword = currentKeyword;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await ref.read(audioRoomProvider.notifier).searchRooms(
            keyword: currentKeyword,
            isRefresh: _currentPage == 1,
          );

      result.fold(
        (error) => throw error,
        (rooms) {
          setState(() {
            if (_currentPage == 1) {
              _dataList = rooms;
            } else {
              _dataList.addAll(rooms);
            }
            _hasMore = rooms.isNotEmpty;
            _error = null;
          });
        },
      );
    } catch (error) {
      setState(() {
        _error = error;
        if (_currentPage > 1) {
          _currentPage--; // 回退页码
        }
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.search),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    height: 40.h,
                    padding:
                        EdgeInsets.symmetric(horizontal: 16.w, vertical: 5.h),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: context.colorScheme.outline),
                    ),
                    child: TextField(
                      controller: _searchController,
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Input the keyword',
                      ),
                    ),
                  ),
                ),
                10.horizontalSpace,
                AppButton(
                  onPressed: () {
                    _onRefresh();
                  },
                  child: Icon(
                    Icons.search,
                    color: context.colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
            12.verticalSpace,
            _buildSearchList(),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchList() {
    // 这里使用key来确保当搜索关键词变化时能够重新创建RefreshListView实例
    return Expanded(
      child: RefreshListView<RoomListItem>(
        key: ValueKey(_searchController.text),
        dataList: _dataList,
        isLoading: _isLoading,
        error: _error,
        hasMore: _hasMore,
        onRefresh: _onRefresh,
        onLoadMore: _onLoadMore,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 1.45,
          crossAxisSpacing: 12.w,
          mainAxisSpacing: 16.h,
        ),
        emptyBuilder: (context) => Center(
          child: Text(context.l10n.noRoomsFound),
        ),
        errorBuilder: (context, error) => Center(
          child: Text(error.toString()),
        ),
        itemBuilder: (context, item, index) => GestureDetector(
            onTap: () => navigateToAudioRoom(item.room?.id ?? ''),
            child: RoomCard(item: item)),
      ),
    );
  }
}
