import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_list_item.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/mixins/join_room_mixin.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/providers/audio_room_provider.dart';
import 'package:flutter_audio_room/features/audio_room/presentation/widgets/audio_room_card.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AudioRoomList extends ConsumerStatefulWidget {
  const AudioRoomList({super.key});

  @override
  ConsumerState<AudioRoomList> createState() => _AudioRoomListState();
}

class _AudioRoomListState extends ConsumerState<AudioRoomList>
    with JoinRoomMixin {

  List<RoomListItem> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onRefresh();
    });
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    _error = null;
    await _fetchRooms();
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;
    _currentPage++;
    await _fetchRooms();
  }

  Future<void> _fetchRooms() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result =
          await ref.read(audioRoomProvider.notifier).getRecommendRooms(
                isRefresh: _currentPage == 1,
              );

      result.fold(
        (error) => throw error,
        (rooms) {
          setState(() {
            if (_currentPage == 1) {
              _dataList = rooms;
            } else {
              _dataList.addAll(rooms);
            }
            _hasMore = rooms.isNotEmpty;
            _error = null;
          });
        },
      );
    } catch (error) {
      setState(() {
        _error = error;
        if (_currentPage > 1) {
          _currentPage--; // 回退页码
        }
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshListView<RoomListItem>(
      dataList: _dataList,
      isLoading: _isLoading,
      error: _error,
      hasMore: _hasMore,
      onRefresh: _onRefresh,
      onLoadMore: _onLoadMore,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 1.45,
        crossAxisSpacing: 12.w,
        mainAxisSpacing: 16.h,
      ),
      emptyBuilder: (context) => Center(
        child: Text(context.l10n.noRoomsFound),
      ),
      itemBuilder: (context, item, index) => GestureDetector(
        onTap: () => navigateToAudioRoom(item.room?.id ?? ''),
        child: RoomCard(item: item),
      ),
    );
  }
}
