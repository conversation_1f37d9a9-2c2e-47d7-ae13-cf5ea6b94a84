// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'product.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

Product _$ProductFromJson(Map<String, dynamic> json) {
  return _Product.fromJson(json);
}

/// @nodoc
mixin _$Product {
  String? get productId => throw _privateConstructorUsedError;
  String? get subscriptionId => throw _privateConstructorUsedError;
  int? get subType => throw _privateConstructorUsedError;
  String? get picUrl => throw _privateConstructorUsedError;
  String get description => throw _privateConstructorUsedError;
  String get currencyCode => throw _privateConstructorUsedError;
  String get currencySymbol => throw _privateConstructorUsedError;
  double get price => throw _privateConstructorUsedError;
  double get discountPrice => throw _privateConstructorUsedError;
  double get discountRate => throw _privateConstructorUsedError;
  int get gems => throw _privateConstructorUsedError;
  int get productOrder => throw _privateConstructorUsedError;
  int get productStatus => throw _privateConstructorUsedError;
  int get isPopular => throw _privateConstructorUsedError;
  int get bestValue => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $ProductCopyWith<Product> get copyWith => throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $ProductCopyWith<$Res> {
  factory $ProductCopyWith(Product value, $Res Function(Product) then) =
      _$ProductCopyWithImpl<$Res, Product>;
  @useResult
  $Res call(
      {String? productId,
      String? subscriptionId,
      int? subType,
      String? picUrl,
      String description,
      String currencyCode,
      String currencySymbol,
      double price,
      double discountPrice,
      double discountRate,
      int gems,
      int productOrder,
      int productStatus,
      int isPopular,
      int bestValue});
}

/// @nodoc
class _$ProductCopyWithImpl<$Res, $Val extends Product>
    implements $ProductCopyWith<$Res> {
  _$ProductCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? subscriptionId = freezed,
    Object? subType = freezed,
    Object? picUrl = freezed,
    Object? description = null,
    Object? currencyCode = null,
    Object? currencySymbol = null,
    Object? price = null,
    Object? discountPrice = null,
    Object? discountRate = null,
    Object? gems = null,
    Object? productOrder = null,
    Object? productStatus = null,
    Object? isPopular = null,
    Object? bestValue = null,
  }) {
    return _then(_value.copyWith(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      subscriptionId: freezed == subscriptionId
          ? _value.subscriptionId
          : subscriptionId // ignore: cast_nullable_to_non_nullable
              as String?,
      subType: freezed == subType
          ? _value.subType
          : subType // ignore: cast_nullable_to_non_nullable
              as int?,
      picUrl: freezed == picUrl
          ? _value.picUrl
          : picUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      discountPrice: null == discountPrice
          ? _value.discountPrice
          : discountPrice // ignore: cast_nullable_to_non_nullable
              as double,
      discountRate: null == discountRate
          ? _value.discountRate
          : discountRate // ignore: cast_nullable_to_non_nullable
              as double,
      gems: null == gems
          ? _value.gems
          : gems // ignore: cast_nullable_to_non_nullable
              as int,
      productOrder: null == productOrder
          ? _value.productOrder
          : productOrder // ignore: cast_nullable_to_non_nullable
              as int,
      productStatus: null == productStatus
          ? _value.productStatus
          : productStatus // ignore: cast_nullable_to_non_nullable
              as int,
      isPopular: null == isPopular
          ? _value.isPopular
          : isPopular // ignore: cast_nullable_to_non_nullable
              as int,
      bestValue: null == bestValue
          ? _value.bestValue
          : bestValue // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$ProductImplCopyWith<$Res> implements $ProductCopyWith<$Res> {
  factory _$$ProductImplCopyWith(
          _$ProductImpl value, $Res Function(_$ProductImpl) then) =
      __$$ProductImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? productId,
      String? subscriptionId,
      int? subType,
      String? picUrl,
      String description,
      String currencyCode,
      String currencySymbol,
      double price,
      double discountPrice,
      double discountRate,
      int gems,
      int productOrder,
      int productStatus,
      int isPopular,
      int bestValue});
}

/// @nodoc
class __$$ProductImplCopyWithImpl<$Res>
    extends _$ProductCopyWithImpl<$Res, _$ProductImpl>
    implements _$$ProductImplCopyWith<$Res> {
  __$$ProductImplCopyWithImpl(
      _$ProductImpl _value, $Res Function(_$ProductImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? productId = freezed,
    Object? subscriptionId = freezed,
    Object? subType = freezed,
    Object? picUrl = freezed,
    Object? description = null,
    Object? currencyCode = null,
    Object? currencySymbol = null,
    Object? price = null,
    Object? discountPrice = null,
    Object? discountRate = null,
    Object? gems = null,
    Object? productOrder = null,
    Object? productStatus = null,
    Object? isPopular = null,
    Object? bestValue = null,
  }) {
    return _then(_$ProductImpl(
      productId: freezed == productId
          ? _value.productId
          : productId // ignore: cast_nullable_to_non_nullable
              as String?,
      subscriptionId: freezed == subscriptionId
          ? _value.subscriptionId
          : subscriptionId // ignore: cast_nullable_to_non_nullable
              as String?,
      subType: freezed == subType
          ? _value.subType
          : subType // ignore: cast_nullable_to_non_nullable
              as int?,
      picUrl: freezed == picUrl
          ? _value.picUrl
          : picUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      description: null == description
          ? _value.description
          : description // ignore: cast_nullable_to_non_nullable
              as String,
      currencyCode: null == currencyCode
          ? _value.currencyCode
          : currencyCode // ignore: cast_nullable_to_non_nullable
              as String,
      currencySymbol: null == currencySymbol
          ? _value.currencySymbol
          : currencySymbol // ignore: cast_nullable_to_non_nullable
              as String,
      price: null == price
          ? _value.price
          : price // ignore: cast_nullable_to_non_nullable
              as double,
      discountPrice: null == discountPrice
          ? _value.discountPrice
          : discountPrice // ignore: cast_nullable_to_non_nullable
              as double,
      discountRate: null == discountRate
          ? _value.discountRate
          : discountRate // ignore: cast_nullable_to_non_nullable
              as double,
      gems: null == gems
          ? _value.gems
          : gems // ignore: cast_nullable_to_non_nullable
              as int,
      productOrder: null == productOrder
          ? _value.productOrder
          : productOrder // ignore: cast_nullable_to_non_nullable
              as int,
      productStatus: null == productStatus
          ? _value.productStatus
          : productStatus // ignore: cast_nullable_to_non_nullable
              as int,
      isPopular: null == isPopular
          ? _value.isPopular
          : isPopular // ignore: cast_nullable_to_non_nullable
              as int,
      bestValue: null == bestValue
          ? _value.bestValue
          : bestValue // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$ProductImpl implements _Product {
  const _$ProductImpl(
      {this.productId,
      this.subscriptionId,
      this.subType,
      this.picUrl,
      this.description = '',
      this.currencyCode = 'USD',
      this.currencySymbol = '\$',
      this.price = 0,
      this.discountPrice = 0,
      this.discountRate = 0,
      this.gems = 0,
      this.productOrder = 0,
      this.productStatus = 0,
      this.isPopular = 0,
      this.bestValue = 0});

  factory _$ProductImpl.fromJson(Map<String, dynamic> json) =>
      _$$ProductImplFromJson(json);

  @override
  final String? productId;
  @override
  final String? subscriptionId;
  @override
  final int? subType;
  @override
  final String? picUrl;
  @override
  @JsonKey()
  final String description;
  @override
  @JsonKey()
  final String currencyCode;
  @override
  @JsonKey()
  final String currencySymbol;
  @override
  @JsonKey()
  final double price;
  @override
  @JsonKey()
  final double discountPrice;
  @override
  @JsonKey()
  final double discountRate;
  @override
  @JsonKey()
  final int gems;
  @override
  @JsonKey()
  final int productOrder;
  @override
  @JsonKey()
  final int productStatus;
  @override
  @JsonKey()
  final int isPopular;
  @override
  @JsonKey()
  final int bestValue;

  @override
  String toString() {
    return 'Product(productId: $productId, subscriptionId: $subscriptionId, subType: $subType, picUrl: $picUrl, description: $description, currencyCode: $currencyCode, currencySymbol: $currencySymbol, price: $price, discountPrice: $discountPrice, discountRate: $discountRate, gems: $gems, productOrder: $productOrder, productStatus: $productStatus, isPopular: $isPopular, bestValue: $bestValue)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$ProductImpl &&
            (identical(other.productId, productId) ||
                other.productId == productId) &&
            (identical(other.subscriptionId, subscriptionId) ||
                other.subscriptionId == subscriptionId) &&
            (identical(other.subType, subType) || other.subType == subType) &&
            (identical(other.picUrl, picUrl) || other.picUrl == picUrl) &&
            (identical(other.description, description) ||
                other.description == description) &&
            (identical(other.currencyCode, currencyCode) ||
                other.currencyCode == currencyCode) &&
            (identical(other.currencySymbol, currencySymbol) ||
                other.currencySymbol == currencySymbol) &&
            (identical(other.price, price) || other.price == price) &&
            (identical(other.discountPrice, discountPrice) ||
                other.discountPrice == discountPrice) &&
            (identical(other.discountRate, discountRate) ||
                other.discountRate == discountRate) &&
            (identical(other.gems, gems) || other.gems == gems) &&
            (identical(other.productOrder, productOrder) ||
                other.productOrder == productOrder) &&
            (identical(other.productStatus, productStatus) ||
                other.productStatus == productStatus) &&
            (identical(other.isPopular, isPopular) ||
                other.isPopular == isPopular) &&
            (identical(other.bestValue, bestValue) ||
                other.bestValue == bestValue));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      productId,
      subscriptionId,
      subType,
      picUrl,
      description,
      currencyCode,
      currencySymbol,
      price,
      discountPrice,
      discountRate,
      gems,
      productOrder,
      productStatus,
      isPopular,
      bestValue);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$ProductImplCopyWith<_$ProductImpl> get copyWith =>
      __$$ProductImplCopyWithImpl<_$ProductImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$ProductImplToJson(
      this,
    );
  }
}

abstract class _Product implements Product {
  const factory _Product(
      {final String? productId,
      final String? subscriptionId,
      final int? subType,
      final String? picUrl,
      final String description,
      final String currencyCode,
      final String currencySymbol,
      final double price,
      final double discountPrice,
      final double discountRate,
      final int gems,
      final int productOrder,
      final int productStatus,
      final int isPopular,
      final int bestValue}) = _$ProductImpl;

  factory _Product.fromJson(Map<String, dynamic> json) = _$ProductImpl.fromJson;

  @override
  String? get productId;
  @override
  String? get subscriptionId;
  @override
  int? get subType;
  @override
  String? get picUrl;
  @override
  String get description;
  @override
  String get currencyCode;
  @override
  String get currencySymbol;
  @override
  double get price;
  @override
  double get discountPrice;
  @override
  double get discountRate;
  @override
  int get gems;
  @override
  int get productOrder;
  @override
  int get productStatus;
  @override
  int get isPopular;
  @override
  int get bestValue;
  @override
  @JsonKey(ignore: true)
  _$$ProductImplCopyWith<_$ProductImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
