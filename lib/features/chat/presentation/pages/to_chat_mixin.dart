import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/chat_page.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/conversation_provider.dart';
import 'package:flutter_audio_room/features/chat/presentation/providers/user_status_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

mixin ToChatMixin {
  void toChat({
    required BuildContext context,
    required WidgetRef ref,
    required String userId,
  }) async {
    final localUserId = ref.read(accountProvider).userInfo?.profile?.id;
    if (localUserId == userId) {
      return;
    }

    LoadingUtils.showLoading();

    final result = await ref
        .read(conversationProvider.notifier)
        .getOrCreateConversation(userId);
    if (result.isLeft()) {
      LoadingUtils.showError(result.getLeft()!.message);
      return;
    }

    LoadingUtils.dismiss();

    final conversation = result.getRight()!;
    if (!context.mounted) return;

    final userRoomStatus = ref.read(userRoomStatusCacheProvider);

    context.push(RoutePageConfig(
      route: ChatPage.route(
        conversation.id,
        conversation.peer,
        userRoomStatus[userId],
      ),
    ));
  }
}
