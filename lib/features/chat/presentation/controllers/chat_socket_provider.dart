import 'dart:async';

import 'package:easy_debounce/easy_throttle.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/features/chat/data/datasources/socket_io_datasource.dart';
import 'package:flutter_audio_room/features/chat/data/repositories/socket_io_repository_impl.dart';
import 'package:flutter_audio_room/services/token_refresh/token_refresh_service.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../data/model/user_online_status_model.dart';
import '../../domain/enums/online_status.dart';
import '../../domain/repositories/socket_io_repository.dart';

part 'chat_socket_provider.g.dart';

// Socket connection state
enum SocketConnectionState { disconnected, connecting, connected, error }

abstract class ChatSocketEvents {
  static const String messageSend = 'msg:send';
  static const String messageRead = 'msg:read';
  static const String typingStarted = 'msg:typing:started';
  static const String typingEnded = 'msg:typing:ended';

  static const String syncAllOfflineMsg = 'conversation:sync:offlineMsg';
  static const String syncOfflineBasic = 'conversation:sync:offlineBasic';
  static const String deleteConversations = 'conversation:delete';
  static const String muteConversation = 'conversation:mute';
  static const String unmuteConversation = 'conversation:unmute';
  static const String pinConversation = 'conversation:pin';
  static const String unpinConversation = 'conversation:unpin';
  static const String conversationMsgTimeoutUpdate =
      'conversation:timeout:update';
  static const String conversationClearNewDeviceTag =
      'conversation:clearNewDeviceTag';
  static const String conversationInfo = 'conversation:info';

  static const String connectInfo = 'connect:info';

  // User online status events
  static const String getUserOnlineInfo = 'getUserOnlineInfo';
}

abstract class ChatSocketListenEvents {
  static const String messageNew = 'msg:new';
  static const String messageRead = 'msg:read';
  static const String typingStarted = 'msg:typing:started';
  static const String typingEnded = 'msg:typing:ended';

  static const String loginExpired = 'login:expired';

  // User status listen events
  static const String chatPeersOnlineStatus = 'chatPeersOnlineStatus';
  static const String userRoomStatus = 'userRoomStatus';
}

// Connection configuration
class SocketConfig {
  static const String socketUrl = 'ws://test.chat.geekermuse.com:9999/chat';
}

@Riverpod(keepAlive: true)
class ChatSocket extends _$ChatSocket {
  Completer<void>? _completer;
  final List<_PendingOperation> _pendingOperations = [];

  // 添加token刷新相关的变量
  DateTime? _lastTokenRefreshTime;
  static const Duration _tokenRefreshThrottleDuration =
      Duration(minutes: 5); // 设置5分钟的限流时间
  SocketIORepository? _repository;

  @override
  SocketConnectionState build() {
    final dataSource = SocketIODataSource();
    _repository = SocketIORepositoryImpl(dataSource);

    ref.onDispose(() {
      _repository?.dispose();
      _repository = null;
      _pendingOperations.clear();
    });

    LogUtils.d('chat socket build success', tag: 'chat socket provider');
    return SocketConnectionState.connecting;
  }

  Future<void> initializeSocket() async {
    _completer = Completer<void>();
    state = SocketConnectionState.connecting;

    LogUtils.d('initialize socket io', tag: 'chat socket provider');
    
    try {
      _repository?.initializeSocket(SocketConfig.socketUrl);
      _setupSocketListeners();
      await _completer?.future;
    } catch (e) {
      _handleConnectionError('初始化失败: $e');
    }
  }
  
  void _setupSocketListeners() {
    _repository?.onConnect((_) {
      state = SocketConnectionState.connected;
      if (_completer?.isCompleted == false) {
        _completer?.complete();
      }
      LogUtils.d('socket io connect', tag: 'chat socket provider');

      // 处理等待中的操作
      _processPendingOperations();
    });

    _repository?.onReconnect((data) {
      // state = SocketConnectionState.connected;
      // if (_completer?.isCompleted == false) {
      //   _completer?.complete();
      // }
      LogUtils.d('socket io reconnect: ${data.toString()}',
          tag: 'chat socket provider');
    });

    _repository?.onConnectError((error) async {
      if (_completer?.isCompleted == false) {
        await _completer?.future;
      }
      _handleConnectionError('连接错误: $error');
      
      // 添加token刷新限流逻辑
      EasyThrottle.throttle(
        'tryRefreshTokenAndReconnect',
        const Duration(seconds: 2),
        () {
          _tryRefreshTokenAndReconnect();
        },
      );
    });

    _repository?.onDisconnect((_) {
      _handleDisconnect('连接断开');
    });

    _repository?.onError((error) {
      _handleConnectionError('Socket错误: $error');
    });
  }
  
  // 新增方法：尝试刷新token并重连，包含限流逻辑
  Future<void> _tryRefreshTokenAndReconnect() async {
    final now = DateTime.now();

    _completer = Completer<void>();

    // 检查是否需要刷新token
    final shouldRefreshToken = _lastTokenRefreshTime == null ||
        now.difference(_lastTokenRefreshTime!) > _tokenRefreshThrottleDuration;

    if (shouldRefreshToken) {
      LogUtils.d('正在刷新token...', tag: 'chat socket provider');

      final result = await getIt<TokenRefreshService>().refreshToken();
      if (result.isRight()) {
        _lastTokenRefreshTime = now;
        LogUtils.d('token刷新成功，正在重连...', tag: 'chat socket provider');
        _repository?.reconnectWithNewToken();
      } else {
        LogUtils.e('token刷新失败', tag: 'chat socket provider');
      }
    } else {
      LogUtils.d('短时间内已刷新过token，直接尝试重连', tag: 'chat socket provider');
    }
  }

  void _handleConnectionError(String errorMessage) {
    state = SocketConnectionState.error;
    if (_completer?.isCompleted == false) {
      _completer?.completeError(errorMessage);
    }
    
    LogUtils.e(errorMessage, tag: 'chat socket provider');
  }
  
  void _handleDisconnect(String reason) {
    state = SocketConnectionState.disconnected;
    _completer = null;
    LogUtils.d('socket io disconnect: $reason', tag: 'chat socket provider');
  }

  // 用于处理异步Socket操作的通用方法
  Future<void> _handleSocketOperation(
    String operationName,
    Future<void> Function() operation,
  ) async {
    try {
      if (state == SocketConnectionState.connected && isConnected) {
        await operation();
      } else if (state == SocketConnectionState.connecting) {
        // 如果正在连接中，添加到待处理队列
        final pendingOp = _PendingOperation(operationName, operation);
        _pendingOperations.add(pendingOp);
        LogUtils.d('Socket正在连接中，操作 "$operationName" 已加入队列',
            tag: 'chat socket provider');
      } else {
        // 如果断开连接，添加到队列并等待Socket.IO自动重连
        final pendingOp = _PendingOperation(operationName, operation);
        _pendingOperations.add(pendingOp);
        LogUtils.d('Socket未连接，操作 "$operationName" 已加入队列等待自动重连',
            tag: 'chat socket provider');
      }
    } catch (e) {
      _logSocketError('执行操作 "$operationName" 时出错: $e', 'chat socket provider');
    }
  }

  void _processPendingOperations() {
    if (_pendingOperations.isEmpty) return;

    LogUtils.d('处理 ${_pendingOperations.length} 个等待中的操作',
        tag: 'chat socket provider');

    final operations = List<_PendingOperation>.from(_pendingOperations);
    _pendingOperations.clear();

    for (final op in operations) {
      op.operation().catchError((e) {
        _logSocketError('执行待处理操作 "${op.name}" 时出错: $e', 'chat socket provider');
      });
    }
  }

  // 用于发送带确认的消息的通用方法
  Future<void> emitWithAck(
    String event,
    Map<String, dynamic> data,
    Function(dynamic) ack,
  ) async {
    await _handleSocketOperation(
      'emitWithAck_$event',
      () async => _repository?.emitWithAck(event, data, ack),
    );
  }

  // 用于发送普通消息的通用方法
  Future<void> _emit(
    String event,
    Map<String, dynamic> data,
    String operationName,
  ) async {
    await _handleSocketOperation(
      operationName,
      () async => _repository?.emit(event, data),
    );
  }

  // 统一的日志记录方法
  void _logSocketError(String message, String tag) {
    LogUtils.e(message, tag: tag);
  }

  Future<void> emitTypingStarted(String conversationId) async {
    await _emit(
      ChatSocketEvents.typingStarted,
      {'conversationId': conversationId},
      'chatSocketProvider.emitTypingStarted',
    );
  }

  Future<void> emitTypingEnded(String conversationId) async {
    await _emit(
      ChatSocketEvents.typingEnded,
      {'conversationId': conversationId},
      'chatSocketProvider.emitTypingEnded',
    );
  }

  Future<void> emitMessageRead({
    required String messageId,
    required bool clearCount,
  }) async {
    LogUtils.d('emitMessageRead: $messageId, $clearCount',
        tag: 'chatSocketProvider');
    await _emit(
      ChatSocketEvents.messageRead,
      {
        'msgId': messageId,
        'clearCount': clearCount,
      },
      'chatSocketProvider.emitMessageRead',
    );
  }

  // Listen to specific event
  Future<void> listenToEvent(String event, Function(dynamic) handler) async {
    await _handleSocketOperation(
      'chatSocketProvider.listenToEvent',
      () async => _repository?.listenToEvent(event, handler),
    );
  }

  // Stop listening to specific event
  Future<void> stopListening(String event) async {
    await _handleSocketOperation(
      'chatSocketProvider.stopListening',
      () async => _repository?.stopListening(event),
    );
  }

  void connect() {
    _repository?.connect();
  }

  void disconnect() {
    _repository?.disconnect();
  }

  // Check if socket is connected
  bool get isConnected => _repository?.isConnected ?? false;

  // User online status methods
  Future<Map<String, UserOnlineStatusModel>> getUserOnlineInfo(
      List<String> userIds) async {
    final completer = Completer<Map<String, UserOnlineStatusModel>>();
    await emitWithAck(
      ChatSocketEvents.getUserOnlineInfo,
      {'userIds': userIds},
      (res) {
        Map<String, UserOnlineStatusModel> userOnlineStatusMap = {};
        try {
          final data = res['data'];
          if (data is Map<String, dynamic>) {
            // Handle server response format: userOnlineStatusList: [1, 2, 1, ...]
            // Status: 1 = online, 2 = offline
            final userOnlineStatusList = data['userOnlineStatusList'] as List?;
            if (userOnlineStatusList != null) {
              // Map the status list to userIds
              for (int i = 0;
                  i < userIds.length && i < userOnlineStatusList.length;
                  i++) {
                final userId = userIds[i];
                final status = userOnlineStatusList[i] as int?;
                if (status != null &&
                    (status == OnlineStatus.online.value ||
                        status == OnlineStatus.offline.value)) {
                  userOnlineStatusMap[userId] = UserOnlineStatusModel(
                    userId: userId,
                    status: status,
                  );
                }
              }
              LogUtils.d(
                  'Updated online status cache for ${userOnlineStatusMap.length} users',
                  tag: 'chatSocketProvider');
            }
          }
        } catch (e) {
          LogUtils.e('Error processing getUserOnlineInfo response: $e',
              tag: 'chatSocketProvider');
        }

        completer.complete(userOnlineStatusMap);
      },
    );
    return completer.future;
  }
}

// 用于存储待处理的Socket操作
class _PendingOperation {
  final String name;
  final Future<void> Function() operation;

  _PendingOperation(this.name, this.operation);
}
