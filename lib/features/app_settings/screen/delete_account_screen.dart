import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/theme/app_colors.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/app_settings/widgets/modal/confirm_delete_account_modal.dart';
import 'package:flutter_audio_room/features/authentication/data/model/contact_info_model.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/authentication/presentation/screens/authentication_main_screen.dart';
import 'package:flutter_audio_room/features/authentication/presentation/widgets/auth_button.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_audio_room/services/core_service/core_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Provider for delete account contact info
final deleteAccountContactInfoProvider =
    FutureProvider.autoDispose<ContactInfoModel?>((ref) async {
  // Call API to get contact info
  final response = await ref.watch(accountProvider.notifier).getContactInfo();

  return response.fold(
    (failure) {
      return null;
    },
    (contactInfo) {
      return contactInfo;
    },
  );
});

// 删除账号步骤
enum DeleteAccountStep {
  verification, // 验证身份
  confirmation, // 确认删除
}

class DeleteAccountScreen extends ConsumerStatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  ConsumerState<DeleteAccountScreen> createState() =>
      _DeleteAccountScreenState();
}

class _DeleteAccountScreenState extends ConsumerState<DeleteAccountScreen> {
  String? _verificationCode;
  bool _isLoading = false;
  bool _isEmailSelected = false;
  bool _codeSent = false;
  
  // 当前步骤
  DeleteAccountStep _currentStep = DeleteAccountStep.verification;

  @override
  Widget build(BuildContext context) {
    // Watch contact info state
    final contactInfoAsyncValue = ref.watch(deleteAccountContactInfoProvider);
    return AppScaffold(
      appBar: AppBar(
        title: const Text('Delete Account'),
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  30.verticalSpace,
                  if (_currentStep == DeleteAccountStep.verification)
                    _buildVerificationSection(contactInfoAsyncValue)
                  else
                    _buildWarningSection(),
                  20.verticalSpace,
                ],
              ),
            ),
          ),
          _buildActionButton(contactInfoAsyncValue),
        ],
      ),
    );
  }

  // Build verification section
  Widget _buildVerificationSection(
      AsyncValue<ContactInfoModel?> contactInfoAsyncValue) {
    return contactInfoAsyncValue.when(
      data: (contactInfo) {
        if (contactInfo == null || !contactInfo.hasContactInfo) {
          return Center(
            child: Text(
              'No contact information available to verify your identity',
              style: context.textTheme.bodyLargeMedium.copyWith(
                color: AppColors.alert,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Verify Identity',
              style: context.textTheme.headlineSmallBold,
            ),
            5.verticalSpace,
            Text(
              'Please select a method to receive verification code:',
              style: context.textTheme.bodyMedium,
            ),
            10.verticalSpace,
            _buildContactSelector(contactInfo),
            20.verticalSpace,
            if (_codeSent) _buildVerificationCodeInput(),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: Colors.red, size: 48.r),
            16.verticalSpace,
            Text(
              'Failed to get contact information',
              style: context.textTheme.bodyLargeMedium.copyWith(
                color: Colors.red,
              ),
            ),
            8.verticalSpace,
            Text(
              error.toString(),
              style: context.textTheme.captionMedium.copyWith(
                color: AppColors.alert,
              ),
              textAlign: TextAlign.center,
            ),
            16.verticalSpace,
            ElevatedButton(
              onPressed: () => ref.refresh(deleteAccountContactInfoProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  // Build contact selector dropdown
  Widget _buildContactSelector(ContactInfoModel contactInfo) {
    List<DropdownMenuItem<bool>> items = [];
    bool onlyOne = false;

    // Check if phone number exists
    if (contactInfo.hasPhone && contactInfo.maskedPhone != null) {
      final phoneDisplay =
          "+${contactInfo.countryNumber ?? ''} ${contactInfo.maskedPhone ?? ''}";
      items.add(DropdownMenuItem(
        value: false,
        child: Row(
          children: [
            Icon(Icons.phone_android,
                color: context.colorScheme.onPrimary, size: 20.r),
            16.horizontalSpace,
            Text(
              'Phone: $phoneDisplay',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      ));
    }

    // Check if email exists
    if (contactInfo.hasEmail && contactInfo.maskedEmail != null) {
      items.add(DropdownMenuItem(
        value: true,
        child: Row(
          children: [
            Icon(Icons.email_outlined,
                color: context.colorScheme.onPrimary, size: 20.r),
            16.horizontalSpace,
            Text(
              'Email: ${contactInfo.maskedEmail}',
              style: context.textTheme.bodyMedium?.copyWith(
                color: context.colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      ));
    }

    onlyOne = items.length == 1;
    if (onlyOne) {
      _isEmailSelected = items[0].value ?? false;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.r, vertical: 12.r),
      decoration: BoxDecoration(
        color: context.colorScheme.primary,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: onlyOne
          ? items[0].child
          : DropdownButtonHideUnderline(
              child: DropdownButton<bool>(
                isExpanded: true,
                value: _isEmailSelected,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _isEmailSelected = value;
                      _codeSent = false;
                      _verificationCode = null;
                    });
                  }
                },
                dropdownColor: context.colorScheme.primary,
                style: context.textTheme.bodyMedium?.copyWith(
                  color: context.colorScheme.onPrimary,
                ),
                icon: Icon(
                  Icons.arrow_drop_down,
                  color: context.colorScheme.onPrimary,
                  size: 24.r,
                ),
                items: items,
              ),
            ),
    );
  }

  // Build verification code input
  Widget _buildVerificationCodeInput() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Enter verification code:',
          style: context.textTheme.bodyMedium,
        ),
        Container(
          decoration: BoxDecoration(
            color: context.theme.brightness == Brightness.light
                ? AppColors.textfieldFillLight
                : AppColors.textfieldFillDark,
            borderRadius: BorderRadius.circular(999.r),
            border: Border.all(
              color: context.colorScheme.outline,
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextField(
                  onChanged: (value) {
                    setState(() {
                      _verificationCode = value;
                    });
                  },
                  keyboardType: TextInputType.number,
                  autofocus: true,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                  ],
                  style: context.textTheme.headlineSmallBold,
                  decoration: InputDecoration(
                    hintText: 'Verification code',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 12.w,
                      vertical: 3.h,
                    ),
                  ),
                ),
              ),
              AppButton(
                onPressed: () => _sendVerificationCode(_isEmailSelected),
                type: AppButtonType.text,
                padding: EdgeInsets.zero,
                child: Text(
                  'Resend code',
                  style: context.textTheme.bodyMedium?.copyWith(
                    color: context.colorScheme.primary,
                  ),
                ),
              ),
              10.horizontalSpace,
            ],
          ),
        ),
      ],
    );
  }

  // Build warning section
  Widget _buildWarningSection() {
    final deleteAccountCooldownDay = ref.watch(
      coreServiceProvider.select(
        (state) => state.config.profileConfigResp.deleteAccountCooldownDay,
      ),
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 添加警告图标
        SizedBox(
          width: 60.w,
          height: 60.w,
          child: Assets.images.deleteAccountEmoji.image(),
        ),
        16.verticalSpace,
        Text(
          'Hide My Profile',
          style: context.textTheme.headlineSmallBold,
        ),
        Text(
          'Your profile will be hidden from others. You won\'t appear in searches, voice matches or friend recommendations. Your data is still stored. You can log in anytime to reactivate your account.',
          style: context.textTheme.bodyMedium,
        ),
        20.verticalSpace,
        Text(
          'Delete My Account',
          style: context.textTheme.headlineSmallBold,
        ),
        Text(
          'Your account and all associated data will be permanently deleted after $deleteAccountCooldownDay days. During this period, you can log in to cancel the deletion.',
          style: context.textTheme.bodyMedium,
        ),
      ],
    );
  }

  // Build action button
  Widget _buildActionButton(
      AsyncValue<ContactInfoModel?> contactInfoAsyncValue) {
    final bool hasContactInfo = contactInfoAsyncValue.maybeWhen(
      data: (contactInfo) => contactInfo?.hasContactInfo ?? false,
      orElse: () => false,
    );

    // 验证页面的按钮
    if (_currentStep == DeleteAccountStep.verification) {
      if (!_codeSent) {
        // 显示发送验证码按钮
        return AuthButton(
          text: 'Send Verification Code',
          disabled: !hasContactInfo,
          isLoading: _isLoading,
          onPressed: () => _sendVerificationCode(_isEmailSelected),
          textStyle: (context, defaultStyle) => defaultStyle.copyWith(
            color: context.colorScheme.surface,
          ),
          backgroundColor: context.colorScheme.primary,
        );
      } else {
        // 显示继续按钮
        final bool canContinue = hasContactInfo &&
            !_isLoading &&
            _verificationCode != null &&
            _verificationCode!.isNotEmpty;

        return AuthButton(
          text: 'Continue',
          disabled: !canContinue,
          isLoading: _isLoading,
          onPressed: () {
            // 验证通过后进入确认页面
            setState(() {
              _currentStep = DeleteAccountStep.confirmation;
            });
          },
          textStyle: (context, defaultStyle) => defaultStyle.copyWith(
            color: context.colorScheme.surface,
          ),
          backgroundColor: context.colorScheme.primary,
        );
      }
    }

    // 确认页面的按钮 - 在底部显示两个按钮
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        AuthButton(
          text: 'Hide My Profile'.toUpperCase(),
          onPressed: () async {
            final result = await ref.read(accountProvider.notifier).hideAccount(
                  useEmail: _isEmailSelected,
                  code: _verificationCode!,
                );
            result.fold(
              (failure) {
                LoadingUtils.showToast(failure.message);
              },
              (right) {
                LoadingUtils.showToast('Profile hidden successfully');
                if (mounted) {
                  context.pushAndRemoveUntil(
                    const WidgetPageConfig(
                      page: AuthenticationMainScreen(),
                    ),
                  );
                }
              },
            );
          },
          textStyle: (context, defaultStyle) => defaultStyle.copyWith(
            color: context.colorScheme.surface,
          ),
          backgroundColor: context.colorScheme.primary,
        ),
        16.verticalSpace,
        AuthButton(
          text: 'Delete My Account',
          type: AppButtonType.outline,
          onPressed: () => _showFinalConfirmDialog(),
          textStyle: (context, defaultStyle) => defaultStyle.copyWith(
            color: context.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  // Send verification code
  Future<void> _sendVerificationCode(bool isEmail) async {
    try {
      LoadingUtils.showLoading();
      // Call send verification code API
      final result =
          await ref.read(accountProvider.notifier).sendVerificationCode2Self(
                isPhone: !isEmail,
              );
      LoadingUtils.dismiss();

      result.fold(
        (failure) {
          LoadingUtils.showToast(failure.message);
        },
        (right) {
          setState(() {
            _codeSent = true;
          });
          LoadingUtils.showToast('Verification code sent');
        },
      );
    } catch (e) {
      LoadingUtils.dismiss();
      LoadingUtils.showToast('Failed to send verification code: $e');
      LogUtils.e('Failed to send verification code: $e',
          tag: 'DeleteAccountScreen');
    }
  }

  // Show final confirmation dialog
  Future<void> _showFinalConfirmDialog() async {
    // 弹出最终确认对话框
    final bool? result = await showAdaptiveDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => const ConfirmDeleteAccountModal(),
    );

    if (result == true) {
      _deleteAccount();
    }
  }

  // Delete account
  Future<void> _deleteAccount() async {
    if (_verificationCode == null) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final contactInfo = ref.read(deleteAccountContactInfoProvider).value;
      if (contactInfo == null) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // Call delete account API
      await ref.read(accountProvider.notifier).deleteAccount(
            useEmail: _isEmailSelected,
            code: _verificationCode!,
          );

      // Logout and return to login page
      if (mounted) {
        context.pushAndRemoveUntil(
          const WidgetPageConfig(
            page: AuthenticationMainScreen(),
          ),
        );
        LoadingUtils.showToast('Account deleted successfully');
      }
    } catch (e) {
      LogUtils.e('Failed to delete account: $e', tag: 'DeleteAccountScreen');
      LoadingUtils.showToast('Failed to delete account: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
