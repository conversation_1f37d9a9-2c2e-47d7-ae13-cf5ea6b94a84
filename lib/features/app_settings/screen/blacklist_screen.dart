import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_button.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/app_settings/mixin/block_mixin.dart';
import 'package:flutter_audio_room/features/app_settings/model/blacklist_item_model.dart';
import 'package:flutter_audio_room/features/app_settings/provider/blacklist_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class BlacklistScreen extends ConsumerStatefulWidget {
  const BlacklistScreen({super.key});

  @override
  ConsumerState<BlacklistScreen> createState() => _BlacklistScreenState();
}

class _BlacklistScreenState extends ConsumerState<BlacklistScreen>
    with BlockMixin {

  List<BlacklistItemModel> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onRefresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      appBar: AppBar(
        title: const Text('Blocked & Reported Users'),
      ),
      body: _buildBlacklist(),
    );
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    _error = null;
    await _fetchBlacklist();
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;
    _currentPage++;
    await _fetchBlacklist();
  }

  Future<void> _fetchBlacklist() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final result = await ref.read(blacklistProvider.notifier).getBlacklist(
            page: _currentPage,
          );

      result.fold(
        (error) => throw error.message,
        (data) {
          setState(() {
            if (_currentPage == 1) {
              _dataList = data.records;
            } else {
              _dataList.addAll(data.records);
            }
            _hasMore = data.records.isNotEmpty;
            _error = null;
          });
        },
      );
    } catch (error) {
      setState(() {
        _error = error;
        if (_currentPage > 1) {
          _currentPage--; // 回退页码
        }
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildBlacklist() {
    return RefreshListView<BlacklistItemModel>(
      dataList: _dataList,
      isLoading: _isLoading,
      error: _error,
      hasMore: _hasMore,
      onRefresh: _onRefresh,
      onLoadMore: _onLoadMore,
      separatorBuilder: (context, index) => 16.verticalSpace,
      emptyBuilder: (context) => Center(
        child: Text(context.l10n.emptyBlacklist),
      ),
      errorBuilder: (context, error) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              error.toString(),
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            TextButton(
              onPressed: _onRefresh,
              child: Text(context.l10n.retry),
            ),
          ],
        ),
      ),
      itemBuilder: (context, item, index) => _buildBlacklistItem(item),
    );
  }

  Widget _buildBlacklistItem(BlacklistItemModel item) {
    return Container(
      decoration: BoxDecoration(
        color: context.colorScheme.tertiary,
        borderRadius: BorderRadius.circular(16.sp),
      ),
      child: Row(
        children: [
          Text(
            item.profile?.nickName ?? '',
          ),
          AppButton(
            type: AppButtonType.outline,
            onPressed: () => unblock(item.profile?.id ?? ''),
            child: const Text('Unblock'),
          ),
        ],
      ),
    );
  }
}
