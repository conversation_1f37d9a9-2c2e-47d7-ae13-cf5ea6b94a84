import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/leaderboard/enum/leaderboard_period.dart';
import 'package:flutter_audio_room/features/leaderboard/model/leaderboard_model.dart';
import 'package:flutter_audio_room/features/leaderboard/widget/leaderboard_list_item.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class LeaderboardList extends ConsumerStatefulWidget {
  const LeaderboardList({
    super.key,
    required this.onLoadPage,
    required this.type,
  });

  final Future<List<LeaderboardModel>> Function(int page) onLoadPage;
  final LeaderboardPeriod type;

  @override
  ConsumerState<LeaderboardList> createState() => _LeaderboardListState();
}

class _LeaderboardListState extends ConsumerState<LeaderboardList> {
  List<LeaderboardModel> _dataList = [];
  bool _isLoading = false;
  dynamic _error;
  bool _hasMore = true;
  int _currentPage = 1;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _onRefresh();
    });
  }

  Future<void> _onRefresh() async {
    _currentPage = 1;
    _error = null;
    await _fetchData();
  }

  Future<void> _onLoadMore() async {
    if (!_hasMore || _isLoading) return;
    _currentPage++;
    await _fetchData();
  }

  Future<void> _fetchData() async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final newItems = await widget.onLoadPage(_currentPage);

      setState(() {
        if (_currentPage == 1) {
          _dataList = newItems;
        } else {
          _dataList.addAll(newItems);
        }
        _hasMore = newItems.isNotEmpty;
        _error = null;
      });
    } catch (error) {
      setState(() {
        _error = error;
        if (_currentPage > 1) {
          _currentPage--; // 回退页码
        }
      });
      rethrow;
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return RefreshListView<LeaderboardModel>(
      dataList: _dataList,
      isLoading: _isLoading,
      error: _error,
      hasMore: _hasMore,
      onRefresh: _onRefresh,
      onLoadMore: _onLoadMore,
      separatorBuilder: (context, index) => SizedBox(height: 10.h),
      emptyBuilder: (context) => const Center(
        child: Text('No data'),
      ),
      itemBuilder: (context, item, index) => LeaderboardListItem(
        leaderboard: item,
        type: widget.type,
        index: index,
      ),
    );
  }
}
