import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_online_status_model.dart';
import 'package:flutter_audio_room/features/chat/data/model/user_room_status_model.dart';
import 'package:flutter_audio_room/features/chat/presentation/widgets/live_tag.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 通用用户列表项组件
///
/// 用于在各种列表中显示用户信息，支持自定义头像、用户信息和操作按钮
class UserListItemCommon extends StatelessWidget {
  /// 用户ID
  final String userId;

  /// 用户头像URL
  final String? avatarUrl;

  /// 头像框SVGA URL
  final String? avatarFrameSvgaUrl;

  /// 头像框图片URL
  final String? avatarFrameImageUrl;

  /// 头像框过期时间
  final DateTime? avatarFrameExpiryTime;

  /// 用户名称
  final String? userName;

  /// 用户描述
  final String? userDescription;

  /// 点击用户项时的回调
  final VoidCallback? onTap;

  /// 是否显示操作按钮
  final bool showActionButton;

  /// 操作按钮
  final Widget? actionButton;

  /// 自定义构建用户信息部分
  final Widget Function(BuildContext context)? userInfoBuilder;

  /// 自定义构建头像部分
  final Widget Function(BuildContext context)? avatarBuilder;

  /// 用户在线状态
  final UserOnlineStatusModel? userOnlineStatus;

  /// 用户房间状态
  final UserRoomStatusModel? userRoomStatus;

  const UserListItemCommon({
    super.key,
    required this.userId,
    this.avatarUrl,
    this.avatarFrameSvgaUrl,
    this.avatarFrameImageUrl,
    this.avatarFrameExpiryTime,
    this.userName,
    this.userDescription,
    this.onTap,
    this.showActionButton = true,
    this.actionButton,
    this.userInfoBuilder,
    this.avatarBuilder,
    this.userOnlineStatus,
    this.userRoomStatus,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: 12.w,
        ),
        decoration: BoxDecoration(
          color: context.colorScheme.surface,
          borderRadius: BorderRadius.circular(12.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            avatarBuilder != null ? avatarBuilder!(context) : _buildAvatar(),
            16.horizontalSpace,
            Expanded(
              child: userInfoBuilder != null
                  ? userInfoBuilder!(context)
                  : _buildUserInfo(context),
            ),
            if (showActionButton && actionButton != null) ...[
              16.horizontalSpace,
              actionButton!,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAvatar() {
    return SizedBox(
      width: 35.w,
      height: 35.w,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // 头像
          Positioned.fill(
            child: AvatarWithFrame(
              avatarUrl: avatarUrl ?? '',
              svgaUrl: avatarFrameSvgaUrl,
              imageUrl: avatarFrameImageUrl,
              expiryTime: avatarFrameExpiryTime,
              width: 35.w,
              height: 35.w,
            ),
          ),

          // Live 标签 - 头像下方
          if (userRoomStatus?.status == 1)
            Positioned(
              bottom: -5.h,
              left: 0,
              right: 0,
              child: LiveTag(userRoomStatus: userRoomStatus),
            ),
        ],
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: userOnlineStatus?.isOnline == true
                    ? context.colorScheme.primary
                    : const Color(0xff939393),
                shape: BoxShape.circle,
              ),
            ),
            5.horizontalSpace,
            Expanded(
              child: Text(
                userName ?? context.l10n.unknownUser,
                style: context.theme.textTheme.bodyLarge?.copyWith(
                  fontSize: 12.sp,
                ),
              ),
            ),
          ],
        ),
        if (userDescription != null) ...[
          4.verticalSpace,
          Text(
            userDescription!,
            style: TextStyle(
              fontSize: 14.sp,
              color: context.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }
}
