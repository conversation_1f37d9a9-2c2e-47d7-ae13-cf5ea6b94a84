// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'gift_states.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

GiftEffectState _$GiftEffectStateFromJson(Map<String, dynamic> json) {
  return _GiftEffectState.fromJson(json);
}

/// @nodoc
mixin _$GiftEffectState {
  String? get svgaUrl => throw _privateConstructorUsedError;
  String? get imageUrl => throw _privateConstructorUsedError;
  GiftType get giftType => throw _privateConstructorUsedError;
  int get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GiftEffectStateCopyWith<GiftEffectState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GiftEffectStateCopyWith<$Res> {
  factory $GiftEffectStateCopyWith(
          GiftEffectState value, $Res Function(GiftEffectState) then) =
      _$GiftEffectStateCopyWithImpl<$Res, GiftEffectState>;
  @useResult
  $Res call(
      {String? svgaUrl, String? imageUrl, GiftType giftType, int timestamp});
}

/// @nodoc
class _$GiftEffectStateCopyWithImpl<$Res, $Val extends GiftEffectState>
    implements $GiftEffectStateCopyWith<$Res> {
  _$GiftEffectStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? svgaUrl = freezed,
    Object? imageUrl = freezed,
    Object? giftType = null,
    Object? timestamp = null,
  }) {
    return _then(_value.copyWith(
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      giftType: null == giftType
          ? _value.giftType
          : giftType // ignore: cast_nullable_to_non_nullable
              as GiftType,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GiftEffectStateImplCopyWith<$Res>
    implements $GiftEffectStateCopyWith<$Res> {
  factory _$$GiftEffectStateImplCopyWith(_$GiftEffectStateImpl value,
          $Res Function(_$GiftEffectStateImpl) then) =
      __$$GiftEffectStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? svgaUrl, String? imageUrl, GiftType giftType, int timestamp});
}

/// @nodoc
class __$$GiftEffectStateImplCopyWithImpl<$Res>
    extends _$GiftEffectStateCopyWithImpl<$Res, _$GiftEffectStateImpl>
    implements _$$GiftEffectStateImplCopyWith<$Res> {
  __$$GiftEffectStateImplCopyWithImpl(
      _$GiftEffectStateImpl _value, $Res Function(_$GiftEffectStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? svgaUrl = freezed,
    Object? imageUrl = freezed,
    Object? giftType = null,
    Object? timestamp = null,
  }) {
    return _then(_$GiftEffectStateImpl(
      svgaUrl: freezed == svgaUrl
          ? _value.svgaUrl
          : svgaUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      imageUrl: freezed == imageUrl
          ? _value.imageUrl
          : imageUrl // ignore: cast_nullable_to_non_nullable
              as String?,
      giftType: null == giftType
          ? _value.giftType
          : giftType // ignore: cast_nullable_to_non_nullable
              as GiftType,
      timestamp: null == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GiftEffectStateImpl implements _GiftEffectState {
  const _$GiftEffectStateImpl(
      {this.svgaUrl,
      this.imageUrl,
      required this.giftType,
      required this.timestamp});

  factory _$GiftEffectStateImpl.fromJson(Map<String, dynamic> json) =>
      _$$GiftEffectStateImplFromJson(json);

  @override
  final String? svgaUrl;
  @override
  final String? imageUrl;
  @override
  final GiftType giftType;
  @override
  final int timestamp;

  @override
  String toString() {
    return 'GiftEffectState(svgaUrl: $svgaUrl, imageUrl: $imageUrl, giftType: $giftType, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GiftEffectStateImpl &&
            (identical(other.svgaUrl, svgaUrl) || other.svgaUrl == svgaUrl) &&
            (identical(other.imageUrl, imageUrl) ||
                other.imageUrl == imageUrl) &&
            (identical(other.giftType, giftType) ||
                other.giftType == giftType) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode =>
      Object.hash(runtimeType, svgaUrl, imageUrl, giftType, timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GiftEffectStateImplCopyWith<_$GiftEffectStateImpl> get copyWith =>
      __$$GiftEffectStateImplCopyWithImpl<_$GiftEffectStateImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GiftEffectStateImplToJson(
      this,
    );
  }
}

abstract class _GiftEffectState implements GiftEffectState {
  const factory _GiftEffectState(
      {final String? svgaUrl,
      final String? imageUrl,
      required final GiftType giftType,
      required final int timestamp}) = _$GiftEffectStateImpl;

  factory _GiftEffectState.fromJson(Map<String, dynamic> json) =
      _$GiftEffectStateImpl.fromJson;

  @override
  String? get svgaUrl;
  @override
  String? get imageUrl;
  @override
  GiftType get giftType;
  @override
  int get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$GiftEffectStateImplCopyWith<_$GiftEffectStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

GiftInfo _$GiftInfoFromJson(Map<String, dynamic> json) {
  return _GiftInfo.fromJson(json);
}

/// @nodoc
mixin _$GiftInfo {
  String? get senderId => throw _privateConstructorUsedError;
  String? get senderName => throw _privateConstructorUsedError;
  List<String> get recipientIds => throw _privateConstructorUsedError;
  int? get giftId => throw _privateConstructorUsedError;
  String? get giftName => throw _privateConstructorUsedError;
  int? get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $GiftInfoCopyWith<GiftInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $GiftInfoCopyWith<$Res> {
  factory $GiftInfoCopyWith(GiftInfo value, $Res Function(GiftInfo) then) =
      _$GiftInfoCopyWithImpl<$Res, GiftInfo>;
  @useResult
  $Res call(
      {String? senderId,
      String? senderName,
      List<String> recipientIds,
      int? giftId,
      String? giftName,
      int? timestamp});
}

/// @nodoc
class _$GiftInfoCopyWithImpl<$Res, $Val extends GiftInfo>
    implements $GiftInfoCopyWith<$Res> {
  _$GiftInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? senderId = freezed,
    Object? senderName = freezed,
    Object? recipientIds = null,
    Object? giftId = freezed,
    Object? giftName = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      senderId: freezed == senderId
          ? _value.senderId
          : senderId // ignore: cast_nullable_to_non_nullable
              as String?,
      senderName: freezed == senderName
          ? _value.senderName
          : senderName // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientIds: null == recipientIds
          ? _value.recipientIds
          : recipientIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      giftId: freezed == giftId
          ? _value.giftId
          : giftId // ignore: cast_nullable_to_non_nullable
              as int?,
      giftName: freezed == giftName
          ? _value.giftName
          : giftName // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$GiftInfoImplCopyWith<$Res>
    implements $GiftInfoCopyWith<$Res> {
  factory _$$GiftInfoImplCopyWith(
          _$GiftInfoImpl value, $Res Function(_$GiftInfoImpl) then) =
      __$$GiftInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? senderId,
      String? senderName,
      List<String> recipientIds,
      int? giftId,
      String? giftName,
      int? timestamp});
}

/// @nodoc
class __$$GiftInfoImplCopyWithImpl<$Res>
    extends _$GiftInfoCopyWithImpl<$Res, _$GiftInfoImpl>
    implements _$$GiftInfoImplCopyWith<$Res> {
  __$$GiftInfoImplCopyWithImpl(
      _$GiftInfoImpl _value, $Res Function(_$GiftInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? senderId = freezed,
    Object? senderName = freezed,
    Object? recipientIds = null,
    Object? giftId = freezed,
    Object? giftName = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$GiftInfoImpl(
      senderId: freezed == senderId
          ? _value.senderId
          : senderId // ignore: cast_nullable_to_non_nullable
              as String?,
      senderName: freezed == senderName
          ? _value.senderName
          : senderName // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientIds: null == recipientIds
          ? _value._recipientIds
          : recipientIds // ignore: cast_nullable_to_non_nullable
              as List<String>,
      giftId: freezed == giftId
          ? _value.giftId
          : giftId // ignore: cast_nullable_to_non_nullable
              as int?,
      giftName: freezed == giftName
          ? _value.giftName
          : giftName // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$GiftInfoImpl implements _GiftInfo {
  const _$GiftInfoImpl(
      {this.senderId,
      this.senderName,
      required final List<String> recipientIds,
      this.giftId,
      this.giftName,
      this.timestamp})
      : _recipientIds = recipientIds;

  factory _$GiftInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$GiftInfoImplFromJson(json);

  @override
  final String? senderId;
  @override
  final String? senderName;
  final List<String> _recipientIds;
  @override
  List<String> get recipientIds {
    if (_recipientIds is EqualUnmodifiableListView) return _recipientIds;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_recipientIds);
  }

  @override
  final int? giftId;
  @override
  final String? giftName;
  @override
  final int? timestamp;

  @override
  String toString() {
    return 'GiftInfo(senderId: $senderId, senderName: $senderName, recipientIds: $recipientIds, giftId: $giftId, giftName: $giftName, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$GiftInfoImpl &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.senderName, senderName) ||
                other.senderName == senderName) &&
            const DeepCollectionEquality()
                .equals(other._recipientIds, _recipientIds) &&
            (identical(other.giftId, giftId) || other.giftId == giftId) &&
            (identical(other.giftName, giftName) ||
                other.giftName == giftName) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      senderId,
      senderName,
      const DeepCollectionEquality().hash(_recipientIds),
      giftId,
      giftName,
      timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$GiftInfoImplCopyWith<_$GiftInfoImpl> get copyWith =>
      __$$GiftInfoImplCopyWithImpl<_$GiftInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$GiftInfoImplToJson(
      this,
    );
  }
}

abstract class _GiftInfo implements GiftInfo {
  const factory _GiftInfo(
      {final String? senderId,
      final String? senderName,
      required final List<String> recipientIds,
      final int? giftId,
      final String? giftName,
      final int? timestamp}) = _$GiftInfoImpl;

  factory _GiftInfo.fromJson(Map<String, dynamic> json) =
      _$GiftInfoImpl.fromJson;

  @override
  String? get senderId;
  @override
  String? get senderName;
  @override
  List<String> get recipientIds;
  @override
  int? get giftId;
  @override
  String? get giftName;
  @override
  int? get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$GiftInfoImplCopyWith<_$GiftInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

FrameInfo _$FrameInfoFromJson(Map<String, dynamic> json) {
  return _FrameInfo.fromJson(json);
}

/// @nodoc
mixin _$FrameInfo {
  String? get senderId => throw _privateConstructorUsedError;
  String? get senderName => throw _privateConstructorUsedError;
  String get recipientId => throw _privateConstructorUsedError;
  int? get frameId => throw _privateConstructorUsedError;
  String? get frameName => throw _privateConstructorUsedError;
  int? get timestamp => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $FrameInfoCopyWith<FrameInfo> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $FrameInfoCopyWith<$Res> {
  factory $FrameInfoCopyWith(FrameInfo value, $Res Function(FrameInfo) then) =
      _$FrameInfoCopyWithImpl<$Res, FrameInfo>;
  @useResult
  $Res call(
      {String? senderId,
      String? senderName,
      String recipientId,
      int? frameId,
      String? frameName,
      int? timestamp});
}

/// @nodoc
class _$FrameInfoCopyWithImpl<$Res, $Val extends FrameInfo>
    implements $FrameInfoCopyWith<$Res> {
  _$FrameInfoCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? senderId = freezed,
    Object? senderName = freezed,
    Object? recipientId = null,
    Object? frameId = freezed,
    Object? frameName = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_value.copyWith(
      senderId: freezed == senderId
          ? _value.senderId
          : senderId // ignore: cast_nullable_to_non_nullable
              as String?,
      senderName: freezed == senderName
          ? _value.senderName
          : senderName // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientId: null == recipientId
          ? _value.recipientId
          : recipientId // ignore: cast_nullable_to_non_nullable
              as String,
      frameId: freezed == frameId
          ? _value.frameId
          : frameId // ignore: cast_nullable_to_non_nullable
              as int?,
      frameName: freezed == frameName
          ? _value.frameName
          : frameName // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$FrameInfoImplCopyWith<$Res>
    implements $FrameInfoCopyWith<$Res> {
  factory _$$FrameInfoImplCopyWith(
          _$FrameInfoImpl value, $Res Function(_$FrameInfoImpl) then) =
      __$$FrameInfoImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {String? senderId,
      String? senderName,
      String recipientId,
      int? frameId,
      String? frameName,
      int? timestamp});
}

/// @nodoc
class __$$FrameInfoImplCopyWithImpl<$Res>
    extends _$FrameInfoCopyWithImpl<$Res, _$FrameInfoImpl>
    implements _$$FrameInfoImplCopyWith<$Res> {
  __$$FrameInfoImplCopyWithImpl(
      _$FrameInfoImpl _value, $Res Function(_$FrameInfoImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? senderId = freezed,
    Object? senderName = freezed,
    Object? recipientId = null,
    Object? frameId = freezed,
    Object? frameName = freezed,
    Object? timestamp = freezed,
  }) {
    return _then(_$FrameInfoImpl(
      senderId: freezed == senderId
          ? _value.senderId
          : senderId // ignore: cast_nullable_to_non_nullable
              as String?,
      senderName: freezed == senderName
          ? _value.senderName
          : senderName // ignore: cast_nullable_to_non_nullable
              as String?,
      recipientId: null == recipientId
          ? _value.recipientId
          : recipientId // ignore: cast_nullable_to_non_nullable
              as String,
      frameId: freezed == frameId
          ? _value.frameId
          : frameId // ignore: cast_nullable_to_non_nullable
              as int?,
      frameName: freezed == frameName
          ? _value.frameName
          : frameName // ignore: cast_nullable_to_non_nullable
              as String?,
      timestamp: freezed == timestamp
          ? _value.timestamp
          : timestamp // ignore: cast_nullable_to_non_nullable
              as int?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$FrameInfoImpl implements _FrameInfo {
  const _$FrameInfoImpl(
      {this.senderId,
      this.senderName,
      required this.recipientId,
      this.frameId,
      this.frameName,
      this.timestamp});

  factory _$FrameInfoImpl.fromJson(Map<String, dynamic> json) =>
      _$$FrameInfoImplFromJson(json);

  @override
  final String? senderId;
  @override
  final String? senderName;
  @override
  final String recipientId;
  @override
  final int? frameId;
  @override
  final String? frameName;
  @override
  final int? timestamp;

  @override
  String toString() {
    return 'FrameInfo(senderId: $senderId, senderName: $senderName, recipientId: $recipientId, frameId: $frameId, frameName: $frameName, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$FrameInfoImpl &&
            (identical(other.senderId, senderId) ||
                other.senderId == senderId) &&
            (identical(other.senderName, senderName) ||
                other.senderName == senderName) &&
            (identical(other.recipientId, recipientId) ||
                other.recipientId == recipientId) &&
            (identical(other.frameId, frameId) || other.frameId == frameId) &&
            (identical(other.frameName, frameName) ||
                other.frameName == frameName) &&
            (identical(other.timestamp, timestamp) ||
                other.timestamp == timestamp));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(runtimeType, senderId, senderName,
      recipientId, frameId, frameName, timestamp);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$FrameInfoImplCopyWith<_$FrameInfoImpl> get copyWith =>
      __$$FrameInfoImplCopyWithImpl<_$FrameInfoImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$FrameInfoImplToJson(
      this,
    );
  }
}

abstract class _FrameInfo implements FrameInfo {
  const factory _FrameInfo(
      {final String? senderId,
      final String? senderName,
      required final String recipientId,
      final int? frameId,
      final String? frameName,
      final int? timestamp}) = _$FrameInfoImpl;

  factory _FrameInfo.fromJson(Map<String, dynamic> json) =
      _$FrameInfoImpl.fromJson;

  @override
  String? get senderId;
  @override
  String? get senderName;
  @override
  String get recipientId;
  @override
  int? get frameId;
  @override
  String? get frameName;
  @override
  int? get timestamp;
  @override
  @JsonKey(ignore: true)
  _$$FrameInfoImplCopyWith<_$FrameInfoImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
