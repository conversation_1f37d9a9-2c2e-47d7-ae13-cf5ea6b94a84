import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/avatar_with_frame.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/to_chat_mixin.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class UserInfoCard extends ConsumerWidget with ToChatMixin {
  const UserInfoCard({
    super.key,
    this.avatar,
    this.svgaUrl,
    this.imageUrl,
    this.expiryTime,
    this.nickName,
    required this.customWidget,
    required this.followerCount,
    required this.followingCount,
    this.onTapFollowing,
    this.onTapFollower,
    required this.userId,
  });

  final String userId;

  final Widget customWidget;

  final String? avatar;

  final String? svgaUrl;

  final String? imageUrl;

  final DateTime? expiryTime;

  final String? nickName;

  final int followerCount;

  final int followingCount;

  final VoidCallback? onTapFollowing;

  final VoidCallback? onTapFollower;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        GestureDetector(
          onTap: () {
            toChat(
              context: context,
              ref: ref,
              userId: userId,
            );
          },
          child: AvatarWithFrame(
            avatarUrl: avatar ?? '',
            svgaUrl: svgaUrl,
            imageUrl: imageUrl,
            expiryTime: expiryTime,
            width: 48.w,
            height: 48.w,
          ),
        ),
        8.horizontalSpace,
        Expanded(
          child: SizedBox(
            height: 48.w,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        nickName ?? 'Unknown Name',
                        style: context.textTheme.bodySmall,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Expanded(
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: customWidget,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: onTapFollowing,
                        child: Text(
                          '${context.l10n.following}: ${followingCount > 999 ? "999+" : followingCount}',
                          style: context.textTheme.bodySmall,
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: onTapFollower,
                        child: Text(
                          '${context.l10n.follower}s: ${followerCount > 999 ? "999+" : followerCount}',
                          style: context.textTheme.bodySmall,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
