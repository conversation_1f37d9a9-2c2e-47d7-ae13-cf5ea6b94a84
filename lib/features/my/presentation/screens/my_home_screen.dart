import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_audio_room/core/extensions/context_ext.dart';
import 'package:flutter_audio_room/core/extensions/navigator_ext.dart';
import 'package:flutter_audio_room/core/theme/app_colors.dart';
import 'package:flutter_audio_room/core/theme/text_theme.dart';
import 'package:flutter_audio_room/core/utils/loading_utils.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/refresh_list_view.dart';
import 'package:flutter_audio_room/features/audio_room/data/model/room_gift_record.dart';
import 'package:flutter_audio_room/features/authentication/presentation/providers/auth_providers.dart';
import 'package:flutter_audio_room/features/home/<USER>/providers/home_provider.dart';
import 'package:flutter_audio_room/features/my/presentation/provider/follow_provider.dart';
import 'package:flutter_audio_room/features/my/presentation/screens/follower_list_screen.dart';
import 'package:flutter_audio_room/features/my/presentation/screens/following_list_screen.dart';
import 'package:flutter_audio_room/features/my/presentation/screens/gift_history_screen.dart';
import 'package:flutter_audio_room/features/my/presentation/widgets/gift_with_user_info.dart';
import 'package:flutter_audio_room/features/my/presentation/widgets/user_info_card.dart';
import 'package:flutter_audio_room/flavors.dart';
import 'package:flutter_audio_room/gen/assets.gen.dart';
import 'package:flutter_audio_room/services/gift_service/presentation/provider/gift_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:url_launcher/url_launcher_string.dart';

class MyHomeScreen extends ConsumerStatefulWidget {
  const MyHomeScreen({super.key});

  @override
  ConsumerState<MyHomeScreen> createState() => _MyHomeScreenState();
}

class _MyHomeScreenState extends ConsumerState<MyHomeScreen> {
  final bool _isLoading = false;
  dynamic _error;

  Future<void> _onRefresh() async {
    await _refreshData();
  }

  Future<void> _onLoadMore() async {
    // 这个页面不需要加载更多功能
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshListView<String>(
        dataList: const [],
        isLoading: _isLoading,
        error: _error,
        hasMore: false,
        onRefresh: _onRefresh,
        onLoadMore: _onLoadMore,
        itemBuilder: (context, item, index) => const SizedBox(),
        itemCount: 0,
        emptyBuilder: (context) => SingleChildScrollView(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            children: [
              20.verticalSpace,
              Builder(builder: (context) {
                final userInfo = ref.watch(accountProvider).userInfo;
                final followState = ref.watch(followProvider);
                return UserInfoCard(
                  userId: userInfo?.profile?.id ?? '',
                  avatar: userInfo?.profile?.avatar,
                  svgaUrl: userInfo?.frame?.svgaUrl,
                  imageUrl: userInfo?.frame?.imageUrl,
                  expiryTime: userInfo?.frame?.activateExpireTime,
                  nickName: userInfo?.profile?.nickName,
                  followerCount: followState.followerCount,
                  followingCount: followState.followingCount,
                  customWidget: GestureDetector(
                    onTap: () {
                      final id = userInfo?.profile?.id;
                      if (id == null) return;
                      Clipboard.setData(ClipboardData(text: id));
                      LoadingUtils.showToast('Copied to clipboard');
                    },
                    child: Text(
                      'ID: ${userInfo?.profile?.id ?? ''}',
                      style: context.textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  onTapFollowing: () {
                    context.push(
                      const WidgetPageConfig(
                        page: FollowingListScreen(),
                      ),
                    );
                  },
                  onTapFollower: () {
                    context.push(
                      const WidgetPageConfig(
                        page: FollowerListScreen(),
                      ),
                    );
                  },
                );
              }),
              40.verticalSpace,
              _buildWallet(),
              10.verticalSpace,
              _alertWithdraw(),
              36.verticalSpace,
              const Divider(),
              27.verticalSpace,
              _buildReceivedGifts(),
              38.verticalSpace,
              _buildSentGifts(),
            ],
          ),
        ),
      ),
    );
  }

  Future<List<String>> _refreshData() async {
    try {
      // Refresh user data including subscription info and personal info
      await ref.read(accountProvider.notifier).refreshUserData();

      // Refresh wallet data
      await ref.read(accountProvider.notifier).getUserWallet();

      return [];
    } catch (e) {
      rethrow;
    }
  }

  Widget _buildWallet() {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () {
              ref.read(homeProvider.notifier).setTab(1);
            },
            child: Card(
              child: Container(
                height: 80.h,
                padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Ghost Coins:',
                      style: context.textTheme.bodySmallSemiBold.copyWith(
                        color: AppColors.ghostCoinColor,
                        height: 1,
                      ),
                    ),
                    6.verticalSpace,
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        SizedBox(
                          width: 30.w,
                          height: 30.w,
                          child: Assets.images.ghostCoin.image(),
                        ),
                        Flexible(
                          child: Text(
                            '${ref.watch(accountProvider).wallet?.gems ?? 0}',
                            style: context.textTheme.headlineLargeSemiBold
                                .copyWith(
                              color: AppColors.ghostCoinColor,
                              height: 1,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        16.horizontalSpace,
        Expanded(
          child: Card(
            child: Container(
              height: 80.h,
              padding: EdgeInsets.symmetric(vertical: 5.h, horizontal: 10.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Total Stars:',
                    style: context.textTheme.bodySmallSemiBold.copyWith(
                      fontSize: 10.sp,
                      color: AppColors.starCoinColor,
                      height: 1,
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: 20.w,
                        width: 20.w,
                        child: Assets.images.starCoin.image(),
                      ),
                      Flexible(
                        child: Text(
                          '${ref.watch(accountProvider).wallet?.dusts ?? 0}',
                          style: context.textTheme.bodyLargeSemiBold.copyWith(
                            color: AppColors.starCoinColor,
                            height: 1,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                  Text(
                    'Cashable Stars: ',
                    style: context.textTheme.bodySmallSemiBold.copyWith(
                      fontSize: 10.sp,
                      color: AppColors.starCoinColor,
                      height: 1,
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      SizedBox(
                        height: 20.w,
                        width: 20.w,
                        child: Assets.images.starCoin.image(),
                      ),
                      Flexible(
                        child: Text(
                          '${ref.watch(accountProvider).wallet?.gemCosts ?? 0}',
                          style: context.textTheme.bodyLargeSemiBold.copyWith(
                            color: AppColors.starCoinColor,
                            height: 1,
                          ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                  FittedBox(
                    child: Text(
                      '50% of your Star Value is withdrawable.',
                      style: context.textTheme.bodySmall?.copyWith(
                        color: context.colorScheme.onSurface.withValues(
                          alpha: 0.5,
                        ),
                      ),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _alertWithdraw() {
    return Align(
      alignment: Alignment.centerRight,
      child: Text.rich(TextSpan(children: [
        TextSpan(
          text: 'Why only 50%? ',
          style: context.textTheme.bodySmall?.copyWith(
            fontSize: 9.sp,
          ),
        ),
        TextSpan(
          text: 'View details.',
          style: context.textTheme.bodySmall?.copyWith(
            fontSize: 9.sp,
            color: context.colorScheme.primary,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              launchUrlString(
                F.privacyPolicyUrl,
              );
            },
        ),
      ])),
    );
  }

  Widget _buildReceivedGifts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${context.l10n.receivedGifts}:',
          style: context.textTheme.bodySmall,
        ),
        13.verticalSpace,
        Builder(builder: (context) {
          final items =
              ref.watch(giftStateNotifierProvider).receivedGifts.records;
          return _buildGiftList(
            items,
            onTapMore: () {
              context.push(
                WidgetPageConfig(
                  page: GiftHistoryScreen(
                    title: context.l10n.receivedGifts,
                    items: items,
                    type: GiftHistoryType.received,
                  ),
                ),
              );
            },
          );
        }),
      ],
    );
  }

  Widget _buildSentGifts() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${context.l10n.sentGifts}:',
          style: context.textTheme.bodySmall,
        ),
        13.verticalSpace,
        Builder(builder: (context) {
          final items = ref.watch(giftStateNotifierProvider).sentGifts.records;
          return _buildGiftList(
            items,
            onTapMore: () {
              context.push(
                WidgetPageConfig(
                  page: GiftHistoryScreen(
                    title: context.l10n.sentGifts,
                    items: items,
                    type: GiftHistoryType.sent,
                  ),
                ),
              );
            },
          );
        }),
      ],
    );
  }

  Widget _buildGiftList(
    List<RoomGiftRecord> items, {
    required VoidCallback onTapMore,
  }) {
    if (items.isEmpty) {
      return SizedBox(
        height: 78.w,
        child: const Center(
          child: Text('No Gifts'),
        ),
      );
    }

    final itemCount = items.length > 4 ? 4 : items.length;

    return SizedBox(
      height: 61.w,
      child: Row(
        children: [
          Expanded(
            child: ListView.separated(
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) {
                return GiftWithUserInfo(item: items[index]);
              },
              separatorBuilder: (context, index) {
                return 10.horizontalSpace;
              },
              itemCount: itemCount,
            ),
          ),
          GestureDetector(
            onTap: onTapMore,
            child: Text(
              'More',
              style: context.textTheme.bodySmall?.copyWith(
                fontSize: 9.sp,
                color: context.colorScheme.primary,
                decoration: TextDecoration.underline,
                decorationColor: context.colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
