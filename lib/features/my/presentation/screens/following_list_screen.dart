import 'package:flutter/material.dart';
import 'package:flutter_audio_room/core/utils/localization_utils.dart';
import 'package:flutter_audio_room/core/widgets/app_scaffold.dart';
import 'package:flutter_audio_room/features/chat/presentation/pages/to_chat_mixin.dart';
import 'package:flutter_audio_room/features/my/presentation/widgets/following_list.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class FollowingListScreen extends ConsumerWidget with ToChatMixin {
  const FollowingListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return AppScaffold(
      appBar: AppBar(
        title: Text(context.l10n.following),
      ),
      body: FollowingList(
        onUserTap: (user) {
          toChat(
            context: context,
            ref: ref,
            userId: user.profile?.id ?? '',
          );
        },
      ),
    );
  }
}
