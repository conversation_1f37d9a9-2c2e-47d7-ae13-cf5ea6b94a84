// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'paginated_response.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

PaginatedResponse<T> _$PaginatedResponseFromJson<T>(
    Map<String, dynamic> json, T Function(Object?) fromJsonT) {
  return _PaginatedResponse<T>.fromJson(json, fromJsonT);
}

/// @nodoc
mixin _$PaginatedResponse<T> {
  int get total => throw _privateConstructorUsedError;
  int get size => throw _privateConstructorUsedError;
  int get current => throw _privateConstructorUsedError;
  List<dynamic> get orders => throw _privateConstructorUsedError;
  int get pages => throw _privateConstructorUsedError;
  bool? get optimizeCountSql => throw _privateConstructorUsedError;
  bool? get searchCount => throw _privateConstructorUsedError;
  int? get maxLimit => throw _privateConstructorUsedError;
  int? get countId => throw _privateConstructorUsedError;
  List<T> get records => throw _privateConstructorUsedError;

  Map<String, dynamic> toJson(Object? Function(T) toJsonT) =>
      throw _privateConstructorUsedError;
  @JsonKey(ignore: true)
  $PaginatedResponseCopyWith<T, PaginatedResponse<T>> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $PaginatedResponseCopyWith<T, $Res> {
  factory $PaginatedResponseCopyWith(PaginatedResponse<T> value,
          $Res Function(PaginatedResponse<T>) then) =
      _$PaginatedResponseCopyWithImpl<T, $Res, PaginatedResponse<T>>;
  @useResult
  $Res call(
      {int total,
      int size,
      int current,
      List<dynamic> orders,
      int pages,
      bool? optimizeCountSql,
      bool? searchCount,
      int? maxLimit,
      int? countId,
      List<T> records});
}

/// @nodoc
class _$PaginatedResponseCopyWithImpl<T, $Res,
        $Val extends PaginatedResponse<T>>
    implements $PaginatedResponseCopyWith<T, $Res> {
  _$PaginatedResponseCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? size = null,
    Object? current = null,
    Object? orders = null,
    Object? pages = null,
    Object? optimizeCountSql = freezed,
    Object? searchCount = freezed,
    Object? maxLimit = freezed,
    Object? countId = freezed,
    Object? records = null,
  }) {
    return _then(_value.copyWith(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      orders: null == orders
          ? _value.orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      pages: null == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int,
      optimizeCountSql: freezed == optimizeCountSql
          ? _value.optimizeCountSql
          : optimizeCountSql // ignore: cast_nullable_to_non_nullable
              as bool?,
      searchCount: freezed == searchCount
          ? _value.searchCount
          : searchCount // ignore: cast_nullable_to_non_nullable
              as bool?,
      maxLimit: freezed == maxLimit
          ? _value.maxLimit
          : maxLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      countId: freezed == countId
          ? _value.countId
          : countId // ignore: cast_nullable_to_non_nullable
              as int?,
      records: null == records
          ? _value.records
          : records // ignore: cast_nullable_to_non_nullable
              as List<T>,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$PaginatedResponseImplCopyWith<T, $Res>
    implements $PaginatedResponseCopyWith<T, $Res> {
  factory _$$PaginatedResponseImplCopyWith(_$PaginatedResponseImpl<T> value,
          $Res Function(_$PaginatedResponseImpl<T>) then) =
      __$$PaginatedResponseImplCopyWithImpl<T, $Res>;
  @override
  @useResult
  $Res call(
      {int total,
      int size,
      int current,
      List<dynamic> orders,
      int pages,
      bool? optimizeCountSql,
      bool? searchCount,
      int? maxLimit,
      int? countId,
      List<T> records});
}

/// @nodoc
class __$$PaginatedResponseImplCopyWithImpl<T, $Res>
    extends _$PaginatedResponseCopyWithImpl<T, $Res, _$PaginatedResponseImpl<T>>
    implements _$$PaginatedResponseImplCopyWith<T, $Res> {
  __$$PaginatedResponseImplCopyWithImpl(_$PaginatedResponseImpl<T> _value,
      $Res Function(_$PaginatedResponseImpl<T>) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? total = null,
    Object? size = null,
    Object? current = null,
    Object? orders = null,
    Object? pages = null,
    Object? optimizeCountSql = freezed,
    Object? searchCount = freezed,
    Object? maxLimit = freezed,
    Object? countId = freezed,
    Object? records = null,
  }) {
    return _then(_$PaginatedResponseImpl<T>(
      total: null == total
          ? _value.total
          : total // ignore: cast_nullable_to_non_nullable
              as int,
      size: null == size
          ? _value.size
          : size // ignore: cast_nullable_to_non_nullable
              as int,
      current: null == current
          ? _value.current
          : current // ignore: cast_nullable_to_non_nullable
              as int,
      orders: null == orders
          ? _value._orders
          : orders // ignore: cast_nullable_to_non_nullable
              as List<dynamic>,
      pages: null == pages
          ? _value.pages
          : pages // ignore: cast_nullable_to_non_nullable
              as int,
      optimizeCountSql: freezed == optimizeCountSql
          ? _value.optimizeCountSql
          : optimizeCountSql // ignore: cast_nullable_to_non_nullable
              as bool?,
      searchCount: freezed == searchCount
          ? _value.searchCount
          : searchCount // ignore: cast_nullable_to_non_nullable
              as bool?,
      maxLimit: freezed == maxLimit
          ? _value.maxLimit
          : maxLimit // ignore: cast_nullable_to_non_nullable
              as int?,
      countId: freezed == countId
          ? _value.countId
          : countId // ignore: cast_nullable_to_non_nullable
              as int?,
      records: null == records
          ? _value._records
          : records // ignore: cast_nullable_to_non_nullable
              as List<T>,
    ));
  }
}

/// @nodoc
@JsonSerializable(genericArgumentFactories: true)
class _$PaginatedResponseImpl<T> implements _PaginatedResponse<T> {
  const _$PaginatedResponseImpl(
      {this.total = 0,
      this.size = 10,
      this.current = 1,
      final List<dynamic> orders = const [],
      this.pages = 0,
      this.optimizeCountSql,
      this.searchCount,
      this.maxLimit,
      this.countId,
      final List<T> records = const []})
      : _orders = orders,
        _records = records;

  factory _$PaginatedResponseImpl.fromJson(
          Map<String, dynamic> json, T Function(Object?) fromJsonT) =>
      _$$PaginatedResponseImplFromJson(json, fromJsonT);

  @override
  @JsonKey()
  final int total;
  @override
  @JsonKey()
  final int size;
  @override
  @JsonKey()
  final int current;
  final List<dynamic> _orders;
  @override
  @JsonKey()
  List<dynamic> get orders {
    if (_orders is EqualUnmodifiableListView) return _orders;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_orders);
  }

  @override
  @JsonKey()
  final int pages;
  @override
  final bool? optimizeCountSql;
  @override
  final bool? searchCount;
  @override
  final int? maxLimit;
  @override
  final int? countId;
  final List<T> _records;
  @override
  @JsonKey()
  List<T> get records {
    if (_records is EqualUnmodifiableListView) return _records;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_records);
  }

  @override
  String toString() {
    return 'PaginatedResponse<$T>(total: $total, size: $size, current: $current, orders: $orders, pages: $pages, optimizeCountSql: $optimizeCountSql, searchCount: $searchCount, maxLimit: $maxLimit, countId: $countId, records: $records)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$PaginatedResponseImpl<T> &&
            (identical(other.total, total) || other.total == total) &&
            (identical(other.size, size) || other.size == size) &&
            (identical(other.current, current) || other.current == current) &&
            const DeepCollectionEquality().equals(other._orders, _orders) &&
            (identical(other.pages, pages) || other.pages == pages) &&
            (identical(other.optimizeCountSql, optimizeCountSql) ||
                other.optimizeCountSql == optimizeCountSql) &&
            (identical(other.searchCount, searchCount) ||
                other.searchCount == searchCount) &&
            (identical(other.maxLimit, maxLimit) ||
                other.maxLimit == maxLimit) &&
            (identical(other.countId, countId) || other.countId == countId) &&
            const DeepCollectionEquality().equals(other._records, _records));
  }

  @JsonKey(ignore: true)
  @override
  int get hashCode => Object.hash(
      runtimeType,
      total,
      size,
      current,
      const DeepCollectionEquality().hash(_orders),
      pages,
      optimizeCountSql,
      searchCount,
      maxLimit,
      countId,
      const DeepCollectionEquality().hash(_records));

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$PaginatedResponseImplCopyWith<T, _$PaginatedResponseImpl<T>>
      get copyWith =>
          __$$PaginatedResponseImplCopyWithImpl<T, _$PaginatedResponseImpl<T>>(
              this, _$identity);

  @override
  Map<String, dynamic> toJson(Object? Function(T) toJsonT) {
    return _$$PaginatedResponseImplToJson<T>(this, toJsonT);
  }
}

abstract class _PaginatedResponse<T> implements PaginatedResponse<T> {
  const factory _PaginatedResponse(
      {final int total,
      final int size,
      final int current,
      final List<dynamic> orders,
      final int pages,
      final bool? optimizeCountSql,
      final bool? searchCount,
      final int? maxLimit,
      final int? countId,
      final List<T> records}) = _$PaginatedResponseImpl<T>;

  factory _PaginatedResponse.fromJson(
          Map<String, dynamic> json, T Function(Object?) fromJsonT) =
      _$PaginatedResponseImpl<T>.fromJson;

  @override
  int get total;
  @override
  int get size;
  @override
  int get current;
  @override
  List<dynamic> get orders;
  @override
  int get pages;
  @override
  bool? get optimizeCountSql;
  @override
  bool? get searchCount;
  @override
  int? get maxLimit;
  @override
  int? get countId;
  @override
  List<T> get records;
  @override
  @JsonKey(ignore: true)
  _$$PaginatedResponseImplCopyWith<T, _$PaginatedResponseImpl<T>>
      get copyWith => throw _privateConstructorUsedError;
}
